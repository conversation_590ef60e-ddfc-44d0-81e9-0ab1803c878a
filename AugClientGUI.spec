# -*- mode: python ; coding: utf-8 -*-
"""
Augment New GUI 版本 v2.0.0 PyInstaller 构建配置
极致压缩单文件可执行程序，需要管理员权限
"""

import os

block_cipher = None

# 收集必要的数据文件（最小化）
datas = [
    # 核心依赖文件
    ('aug_client.py', '.'),
    ('vscode_cache_cleaner.py', '.'),
    ('imap_verification_api.py', '.'),

    # 配置和帮助文件
    ('aug.env', '.'),
    ('version.txt', '.'),
    ('first_use.txt', '.'),
    ('solution.txt', '.'),
    ('favicon.ico', '.'),
]

# 添加可选文件（如果存在）
optional_files = ['requirements.txt', 'usage.json', 'registered_accounts.json']
for file in optional_files:
    if os.path.exists(file):
        datas.append((file, '.'))

a = Analysis(
    ['aug_client_gui.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # 核心 PyQt5 模块
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',

        # 必要的网络模块
        'requests',
        'urllib3',
        'ssl',
        'certifi',

        # 系统操作
        'subprocess',
        'platform',
        'shutil',
        'tempfile',
        'dotenv',
        'json',
        'threading',
        'webbrowser',
        'importlib.util',

        # 版本管理
        'packaging.version',

        # 邮件模块
        'imaplib',
        'email.mime.text',
        'email.header',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 大幅排除不需要的模块
        'tkinter', 'tk', 'tcl',
        'matplotlib', 'numpy', 'pandas', 'scipy',
        'PIL', 'Pillow', 'cv2', 'opencv',
        'tensorflow', 'torch', 'keras',
        'jupyter', 'IPython', 'notebook',
        'django', 'flask', 'fastapi',
        'sqlalchemy', 'sqlite3',
        'xml', 'xmlrpc', 'html',
        'http.server', 'socketserver',
        'multiprocessing', 'concurrent.futures',
        'asyncio', 'aiohttp',
        'pytest', 'unittest', 'doctest',
        'pdb', 'profile', 'cProfile',
        'distutils', 'setuptools', 'pip',
        'wheel', 'pkg_resources',
        '_pytest', 'py',
        'babel', 'jinja2', 'markupsafe',
        'cryptography', 'pyopenssl',
        'psutil', 'win32api', 'win32con',
        'pywintypes', 'pythoncom',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=True,  # 启用 noarchive 以减小体积
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 创建极致压缩的单文件可执行程序
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AugmentNew_2.0.0',                   # 更新版本号
    debug=False,                               # 发布版本
    bootloader_ignore_signals=False,
    strip=True,                                # 移除符号信息以减小体积
    upx=True,                                  # 启用 UPX 压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,                             # GUI 版本不显示控制台
    disable_windowed_traceback=True,           # 禁用窗口化回溯以减小体积
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='favicon.ico',                        # 程序图标
    uac_admin=True,                            # 需要管理员权限
    uac_uiaccess=False,                        # 不需要 UI 访问权限
)
