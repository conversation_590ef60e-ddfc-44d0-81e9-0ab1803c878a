# -*- mode: python ; coding: utf-8 -*-
"""
Augment New GUI 版本 PyInstaller 构建配置
包含所有必要的依赖文件和资源
"""

import os
from PyInstaller.utils.hooks import collect_data_files

block_cipher = None

# 收集所有需要的数据文件
datas = [
    # 核心依赖文件
    ('aug_client.py', '.'),                    # GUI 依赖的核心功能模块
    ('vscode_cache_cleaner.py', '.'),          # 缓存清理模块
    ('imap_verification_api.py', '.'),         # 验证码获取 API

    # 配置文件
    ('aug.env', '.'),                          # 主配置文件
    ('version.txt', '.'),                      # 版本信息文件

    # 帮助文档
    ('first_use.txt', '.'),                    # 第一次使用指南
    ('solution.txt', '.'),                     # 问题解决方案

    # 图标和资源
    ('favicon.ico', '.'),                      # 程序图标

    # 可选的配置文件（如果存在）
    ('requirements.txt', '.') if os.path.exists('requirements.txt') else None,
    ('usage.json', '.') if os.path.exists('usage.json') else None,
    ('registered_accounts.json', '.') if os.path.exists('registered_accounts.json') else None,
]

# 过滤掉 None 值
datas = [item for item in datas if item is not None]

a = Analysis(
    ['aug_client_gui.py'],                     # 主程序入口
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        # PyQt5 相关
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'PyQt5.QtNetwork',

        # 网络和加密相关
        'requests',
        'urllib3',
        'ssl',
        'certifi',

        # 系统和文件操作
        'subprocess',
        'platform',
        'shutil',
        'glob',
        'pathlib',

        # 配置和环境
        'dotenv',
        'os',
        'sys',

        # 数据处理
        'json',
        'time',
        'datetime',
        'random',
        'string',

        # 版本管理
        'packaging',
        'packaging.version',

        # 邮件和验证码相关
        'imaplib',
        'email',
        'email.mime',
        'email.mime.text',
        'email.mime.multipart',
        'email.header',
        'email.utils',

        # 线程和异步
        'threading',
        'queue',

        # 其他可能需要的模块
        'webbrowser',
        'importlib',
        'importlib.util',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小体积
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

# 创建单文件可执行程序
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AugmentNew_1.0.0',                   # 可执行文件名
    debug=False,                               # 发布版本不启用调试
    bootloader_ignore_signals=False,
    strip=False,                               # 保留符号信息以便调试
    upx=True,                                  # 启用 UPX 压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,                             # GUI 版本不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='favicon.ico',                        # 程序图标
    uac_admin=False,                           # 不需要管理员权限
    version='version_info.txt' if os.path.exists('version_info.txt') else None,  # 版本信息
)
