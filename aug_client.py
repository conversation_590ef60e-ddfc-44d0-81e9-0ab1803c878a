#!/usr/bin/env python3
"""
Augment Code 客户端

Augment Code 官方客户端工具，用于自动化处理用户注册、验证码获取和环境配置。
提供完整的 VSCode 缓存清理和邮箱验证服务。

版权所有 © Augment Code
"""

import os
import sys
import time
import random
import string
import requests
import subprocess
from packaging import version
from dotenv import load_dotenv

# 获取资源路径的辅助函数
def resource_path(relative_path):
    """获取资源的绝对路径，适用于开发环境和 PyInstaller 打包后的环境"""
    try:
        # PyInstaller 创建临时文件夹并将路径存储在 _MEIPASS 中
        base_path = sys._MEIPASS
    except Exception:
        # 如果不是在 PyInstaller 环境中，使用当前目录
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

# ==================== 配置常量 ====================
# Augment Code API 服务器地址
API_BASE_URL = "https://augment.vchat.xin"

# VSCode 缓存清理脚本路径
VSCODE_CLEANER_SCRIPT = resource_path("vscode_cache_cleaner.py")

# 当前客户端版本
CURRENT_VERSION = "2.0.0"

# 配置文件路径
ENV_FILE = "aug.env"

# 验证码获取配置
MAX_RETRIES = 11  # 最大重试次数：11次 × 15秒 ≈ 170秒
RETRY_INTERVAL = 15  # 重试间隔（秒）

# Augment Code 官方注册页面
AUGMENT_REGISTER_URL = "https://app.augmentcode.com/"

def print_header():
    """显示程序标题和版本信息。"""
    print()
    print("██████╗ ██╗   ██╗ ██████╗ ███╗   ███╗███████╗███╗   ██╗████████╗    ███╗   ██╗███████╗██╗    ██╗")
    print("██╔══██╗██║   ██║██╔════╝ ████╗ ████║██╔════╝████╗  ██║╚══██╔══╝    ████╗  ██║██╔════╝██║    ██║")
    print("███████║██║   ██║██║  ███╗██╔████╔██║█████╗  ██╔██╗ ██║   ██║       ██╔██╗ ██║█████╗  ██║ █╗ ██║")
    print("██╔══██║██║   ██║██║   ██║██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║       ██║╚██╗██║██╔══╝  ██║███╗██║")
    print("██║  ██║╚██████╔╝╚██████╔╝██║ ╚═╝ ██║███████╗██║ ╚████║   ██║       ██║ ╚████║███████╗╚███╔███╔╝")
    print("╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝       ╚═╝  ╚═══╝╚══════╝ ╚══╝╚══╝ ")
    print()
    print(f"Augment New 官方客户端 v{CURRENT_VERSION}".center(100))
    print("VSCode 环境优化 | 验证码服务".center(100))
    print("=" * 100)
    print()

def check_version():
    """
    检查客户端版本是否为最新版本。

    返回:
        bool: 版本检查通过返回 True，需要更新返回 False
    """
    print("🔍 正在检查客户端版本...")

    try:
        response = requests.get(f"{API_BASE_URL}/version", timeout=10)

        if response.status_code != 200:
            print(f"❌ 版本检查失败，服务器响应异常 (状态码: {response.status_code})")
            return False

        data = response.json()

        if not data.get("status"):
            print(f"❌ 版本检查失败: {data.get('message', '服务器返回未知错误')}")
            return False

        server_version = data.get("version")
        download_url = data.get("download_url")

        print(f"📦 当前客户端版本: v{CURRENT_VERSION}")
        print(f"🌐 服务器最新版本: v{server_version}")

        # 版本比较
        if version.parse(server_version) > version.parse(CURRENT_VERSION):
            print("\n🆕 发现新版本可用！")
            print(f"📥 下载地址: {download_url}")
            print("\n⚠️  为确保功能正常，请更新到最新版本后再使用。")
            return False

        print("✅ 版本检查通过，当前为最新版本。")
        return True

    except requests.exceptions.Timeout:
        print("❌ 版本检查超时，请检查网络连接。")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 Augment New 服务器，请检查网络连接。")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 版本检查发生未知错误: {str(e)}")
        return False

def check_terms_agreement():
    """
    检查用户是否已同意 Augment Code 服务条款。

    返回:
        bool: 用户已同意条款返回 True，否则返回 False
    """
    # 检查配置文件中的条款同意状态
    if os.path.exists(ENV_FILE):
        load_dotenv(ENV_FILE)
        terms_agreed = os.getenv("TERMS")

        if terms_agreed and terms_agreed.lower() == "true":
            print("📋 您已同意 Augment New 服务条款。")
            print("💰 代售返利计划: https://aug.cursorx.pw/rebate.html")
            return True

    # 显示服务条款
    print("\n" + "=" * 80)
    print("📜 AUGMENT NEW 服务条款".center(80))
    print("=" * 80)
    print()
    print("💡 这可能是您首次使用 AugmentNew，因此您此次可能需要填写某些配置，但是这些配置将会在后续不会再询问")
    print()
    print("⚠️  使用 Augment NEW 客户端前，请仔细阅读并同意以下条款：")
    print()
    print("🚫 1. 禁止以任何形式对本软件进行解包、反编译、逆向工程")
    print("🚫 2. 禁止对本软件进行二次开发、修改或衍生")
    print("🚫 3. 禁止将本软件用于商业用途或进行贩卖")
    print("🚫 4. 禁止传播、分发本软件的源代码或可执行文件")
    print("✅ 5. 本软件仅供个人学习和合法使用")
    print("⚖️ 6. 违反上述条款将承担相应的法律责任")
    print()
    print("📄 内部福利：代售返利请访问: https://aug.cursorx.pw/rebate.html")
    print("=" * 80)
    print()

    # 用户确认流程
    while True:
        print("💬 请输入 '我已知悉' 以同意上述条款并继续使用：")
        user_input = input("👉 ").strip()

        if user_input == "我已知悉":
            save_terms_agreement()
            print("✅ 感谢您同意 Augment New 服务条款！")
            return True
        else:
            print("❌ 输入不正确，请准确输入 '我已知悉' 以继续。")

def save_terms_agreement():
    """
    保存条款同意状态到环境文件。
    """
    # 读取现有的环境变量
    env_vars = {}
    if os.path.exists(ENV_FILE):
        with open(ENV_FILE, "r", encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and "=" in line:
                    key, value = line.split("=", 1)
                    env_vars[key] = value

    # 添加条款同意状态
    env_vars["TERMS"] = "true"

    # 添加默认的 Cursor 配置（如果不存在）
    if "CURSOR" not in env_vars:
        env_vars["CURSOR"] = "false"
    if "CURSORMENTION" not in env_vars:
        env_vars["CURSORMENTION"] = "false"

    # 写回文件
    with open(ENV_FILE, "w", encoding='utf-8') as f:
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")

def load_or_get_code():
    """
    从配置文件加载访问代码或提示用户输入。

    返回:
        str: 用户访问代码
    """
    # 尝试从配置文件加载已保存的访问代码
    if os.path.exists(ENV_FILE):
        load_dotenv(ENV_FILE)
        code = os.getenv("AUG_CODE")

        if code:
            print(f"🔑 已从配置文件加载访问代码。")
            return code

    # 提示用户输入新的访问代码
    print("如果您没有 Augement New 访问代码，请在 https://afdian.com/a/suaolin 购买。")
    print("🔐 请输入您的 Augment New 访问代码:")
    code = input("👉 ").strip()

    # 保存访问代码到配置文件（保持其他配置不变）
    env_vars = {}
    if os.path.exists(ENV_FILE):
        with open(ENV_FILE, "r", encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and "=" in line:
                    key, value = line.split("=", 1)
                    env_vars[key] = value

    env_vars["AUG_CODE"] = code

    # 添加默认的 Cursor 配置（如果不存在）
    if "CURSOR" not in env_vars:
        env_vars["CURSOR"] = "false"
    if "CURSORMENTION" not in env_vars:
        env_vars["CURSORMENTION"] = "false"

    with open(ENV_FILE, "w", encoding='utf-8') as f:
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")

    print(f"💾 访问代码已保存到配置文件。")
    return code

def verify_code(code):
    """
    验证用户访问代码的有效性。

    参数:
        code (str): 用户访问代码

    返回:
        bool: 代码有效返回 True，无效返回 False
    """
    print("🔍 正在验证访问代码...")

    try:
        response = requests.get(f"{API_BASE_URL}/?code={code}", timeout=10)

        if response.status_code != 200:
            print(f"❌ 访问代码验证失败，服务器响应异常 (状态码: {response.status_code})")
            return False

        data = response.json()

        if not data.get("status"):
            print(f"❌ 访问代码无效: {data.get('message', '请检查代码是否正确')}")
            return False

        code_data = data.get("code_data", {})
        usage = code_data.get("usage", 0)
        max_usage = code_data.get("max_usage", 0)

        print(f"✅ 访问代码验证成功！使用次数: {usage}/{max_usage}")
        return True

    except requests.exceptions.Timeout:
        print("❌ 访问代码验证超时，请检查网络连接。")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 Augment New 服务器，请检查网络连接。")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 访问代码验证发生未知错误: {str(e)}")
        return False

def get_code_usage_info(code):
    """
    获取访问代码的详细使用信息。

    参数:
        code (str): 用户访问代码

    返回:
        dict: 包含使用信息的字典，失败返回 None
    """
    try:
        response = requests.get(f"{API_BASE_URL}/?code={code}", timeout=10)

        if response.status_code != 200:
            return None

        data = response.json()

        if not data.get("status"):
            return None

        code_data = data.get("code_data", {})
        usage = code_data.get("usage", 0)
        max_usage = code_data.get("max_usage", 0)

        return {
            "usage": usage,
            "max_usage": max_usage,
            "remaining": max_usage - usage,
            "valid": True
        }

    except Exception:
        return None

def get_mail_domains():
    """
    从 Augment Code 服务器获取可用的临时账户。

    返回:
        list: 可用账户列表
    """
    print("🌐 正在获取可用的账户...")

    try:
        response = requests.get(f"{API_BASE_URL}/mail", timeout=10)

        if response.status_code != 200:
            print(f"❌ 获取账户失败，服务器响应异常 (状态码: {response.status_code})")
            return []

        data = response.json()

        if not data.get("status"):
            print(f"❌ 获取账户失败: {data.get('message', '服务器返回未知错误')}")
            return []

        content = data.get("content", "")
        domains = [domain.strip() for domain in content.split("\n") if domain.strip()]

        if not domains:
            print("❌ 当前没有可用的账户，请稍后重试。")
            return []

        print(f"✅ 成功获取到可用的账户。")
        return domains

    except requests.exceptions.Timeout:
        print("❌ 获取账户超时，请检查网络连接。")
        return []
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到 Augment New 服务器，请检查网络连接。")
        return []
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {str(e)}")
        return []
    except Exception as e:
        print(f"❌ 获取账户发生未知错误: {str(e)}")
        return []

def generate_random_email(domains):
    """
    生成随机临时邮箱地址用于 Augment Code 注册。

    参数:
        domains (list): 可用账户列表

    返回:
        str: 生成的随机邮箱地址
    """
    # 生成随机用户名（8位字母数字组合）
    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))

    # 随机选择一个可用域名
    domain = random.choice(domains)

    email = f"{username}@{domain}"
    print(f"📧 已获取到账户邮箱: {email}")
    print(f"💡 请将此邮箱复制到 Augment Code 注册页面使用")

    return email

def check_cursor_config():
    """
    检查和处理 Cursor 缓存清理配置。

    返回:
        bool: 是否清理 Cursor 缓存
    """
    # 加载环境变量
    if os.path.exists(ENV_FILE):
        load_dotenv(ENV_FILE)
        cursor_enabled = os.getenv("CURSOR", "false").lower() == "true"
        cursor_mentioned = os.getenv("CURSORMENTION", "false").lower() == "true"

        if cursor_mentioned:
            # 已经提示过，直接输出状态
            if cursor_enabled:
                print("✅ 您已默认清理 Cursor 缓存")
                print("💡 如果后续想更改请编辑本软件同目录下 aug.env 的 CURSOR 变量")
            return cursor_enabled
        else:
            # 首次询问用户
            print("\n" + "=" * 80)
            print("🖱️  CURSOR 缓存清理设置".center(80))
            print("=" * 80)
            print()
            print("💡 检测到您可能使用 Cursor AI 代码编辑器")
            print("🧹 是否同时清理 Cursor 的缓存文件以优化性能？")
            print()
            print("📝 选择说明：")
            print("   Y - 是，同时清理 VSCode 和 Cursor 缓存")
            print("   N - 否，仅清理 VSCode 缓存")
            print()

            while True:
                choice = input("👉 请选择 (Y/N): ").strip().upper()
                if choice in ['Y', 'YES', '是']:
                    cursor_enabled = True
                    break
                elif choice in ['N', 'NO', '否']:
                    cursor_enabled = False
                    break
                else:
                    print("❌ 请输入 Y 或 N")

            # 保存用户选择
            save_cursor_config(cursor_enabled)

            if cursor_enabled:
                print("✅ 已设置为同时清理 VSCode 和 Cursor 缓存")
            else:
                print("✅ 已设置为仅清理 VSCode 缓存")
            print("💡 如需修改此设置，请编辑本软件同目录下 aug.env 的 CURSOR 变量")

            return cursor_enabled
    else:
        # 配置文件不存在，返回默认值
        return False

def save_cursor_config(cursor_enabled):
    """
    保存 Cursor 配置到环境文件。

    参数:
        cursor_enabled (bool): 是否启用 Cursor 缓存清理
    """
    # 读取现有的环境变量
    env_vars = {}
    if os.path.exists(ENV_FILE):
        with open(ENV_FILE, "r", encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and "=" in line:
                    key, value = line.split("=", 1)
                    env_vars[key] = value

    # 更新 Cursor 配置
    env_vars["CURSOR"] = "true" if cursor_enabled else "false"
    env_vars["CURSORMENTION"] = "true"  # 标记已经询问过用户

    # 写回文件
    with open(ENV_FILE, "w", encoding='utf-8') as f:
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")

def get_verification_code(code, email):
    """
    自动获取 Augment Code 注册验证码。

    参数:
        code (str): 用户访问代码
        email (str): 临时邮箱地址

    返回:
        str: 成功返回验证码，失败返回 None
    """
    print(f"📧 正在为 {email} 获取验证码")
    print(f"🌐 请尽快使用此邮箱在 Augment Code 注册页面完成注册: {AUGMENT_REGISTER_URL}")
    print("🔄 正在监听来自 Augment Code 的验证码...")

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            response = requests.get(f"{API_BASE_URL}/verify?code={code}&to={email}", timeout=15)

            if response.status_code == 200:
                data = response.json()

                if data.get("status"):
                    verification_code = data.get("verification_code")
                    print(f"✅ 验证码获取成功: {verification_code}")
                    return verification_code

        except requests.exceptions.RequestException:
            pass  # 静默处理网络错误，避免干扰用户体验
        except Exception:
            pass  # 静默处理其他异常

        if attempt < MAX_RETRIES:
            time.sleep(RETRY_INTERVAL)

    print("⏰ 验证码获取超时，请稍后重试或检查邮箱注册状态。")
    return None

def clean_vscode_cache(selected_apps=None):
    """
    自动清理选定 IDE 的缓存以优化开发环境。

    参数:
        selected_apps (list): 要清理的应用列表，如果为 None 则使用配置文件中的设置

    返回:
        bool: 清理成功返回 True，失败返回 False
    """
    # 如果没有指定应用列表，从配置文件加载
    if selected_apps is None:
        if os.path.exists(ENV_FILE):
            load_dotenv(ENV_FILE)
            selected_apps_str = os.getenv("SELECTED_APPS", "vscode")
            selected_apps = [app.strip() for app in selected_apps_str.split(",") if app.strip()]
        else:
            selected_apps = ['vscode']

    if not selected_apps:
        selected_apps = ['vscode']

    app_names = [app.upper() for app in selected_apps]
    print(f"🧹 正在优化 {', '.join(app_names)} 开发环境...")
    print(f"🔄 自动关闭 {', '.join(app_names)} 进程并清理缓存文件...")
    print("💡 程序以普通用户权限运行，将清理可访问的缓存文件")

    kill_option = True  # 自动关闭进程

    try:
        # 在打包环境中，直接导入并调用清理函数，而不是通过子进程
        if getattr(sys, 'frozen', False):
            print("在打包环境中直接执行清理...")

            # 导入清理模块
            import importlib.util
            import tempfile

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.py', delete=False) as temp_file:
                temp_script_path = temp_file.name
                with open(VSCODE_CLEANER_SCRIPT, 'r', encoding='utf-8') as f:
                    script_content = f.read()
                    temp_file.write(script_content.encode('utf-8'))

            try:
                # 导入临时模块
                spec = importlib.util.spec_from_file_location("vscode_cleaner", temp_script_path)
                cleaner_module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(cleaner_module)

                # 直接调用模块中的函数
                print("正在清理 VSCode 缓存，请稍候...")

                # 如果用户选择关闭进程
                if kill_option:
                    print(f"正在关闭 {', '.join(app_names)} 进程...")
                    cleaner_module.kill_ide_processes(selected_apps)

                # 获取缓存路径
                cache_paths = cleaner_module.get_ide_cache_paths(selected_apps)

                # 清理缓存
                success_count = 0
                total_paths = len(cache_paths)

                for name, path in cache_paths.items():
                    print(f"正在处理 {name}，位置: {path}")
                    try:
                        if cleaner_module.clear_cache(path, False, None):
                            success_count += 1
                    except Exception as e:
                        print(f"处理 {name} 时出错: {str(e)}")

                if success_count > 0:
                    print(f"✅ {', '.join(app_names)} 缓存清理完成: {success_count}/{total_paths} 个位置已成功处理")
                    print(f"🎉 {', '.join(app_names)} 开发环境优化成功！")
                    return True
                else:
                    print("❌ 缓存清理失败: 没有成功清理任何缓存位置")
                    return False

            except Exception as e:
                print(f"导入或执行清理模块时出错: {str(e)}")
                import traceback
                print(traceback.format_exc())
                return False
            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_script_path)
                except:
                    pass
        else:
            # 在开发环境中，使用子进程运行脚本
            cmd = [sys.executable, VSCODE_CLEANER_SCRIPT]

            if kill_option:
                cmd.append("--kill")

            # 为每个选定的应用添加参数
            for app in selected_apps:
                if app == 'cursor':
                    cmd.append("--cursor")
                elif app == 'idea':
                    cmd.append("--idea")
                elif app == 'pycharm':
                    cmd.append("--pycharm")

            print(f"正在清理 {', '.join(app_names)} 缓存，请稍候...")
            process = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                   encoding='utf-8', errors='replace')

            # 打印输出，帮助调试
            if process.stdout:
                print("脚本输出:")
                print(process.stdout)

            if process.returncode == 0:
                print(f"✅ {', '.join(app_names)} 开发环境优化成功！")
                return True
            else:
                print(f"❌ 缓存清理失败: {process.stderr}")
                return False
    except Exception as e:
        print(f"运行缓存清理脚本时出错: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """Augment Code 客户端主程序。"""
    print_header()

    try:
        # 步骤 1: 服务条款确认
        if not check_terms_agreement():
            print("\n❌ 必须同意服务条款才能继续使用 Augment New 客户端。")
            input("\n按回车键退出...")
            return

        print("\n" + "-" * 100 + "\n")

        # 步骤 2: 版本检查
        if not check_version():
            print("\n❌ 版本检查失败，请更新到最新版本后再使用。")
            input("\n按回车键退出...")
            return

        print("\n" + "-" * 100 + "\n")

        # 步骤 3: 访问代码验证
        code_valid = False
        while not code_valid:
            code = load_or_get_code()
            if verify_code(code):
                code_valid = True
            else:
                print("\n❌ 访问代码验证失败，请重新输入正确的代码。")
                # 清除无效的配置文件
                if os.path.exists(ENV_FILE):
                    os.remove(ENV_FILE)

        print("\n" + "-" * 100 + "\n")

        # 步骤 4: VSCode 环境优化
        clean_vscode_cache()

        print("\n" + "-" * 100 + "\n")

        # 步骤 5: 临时邮箱生成
        domains = get_mail_domains()
        if not domains:
            print("\n❌ 获取邮件域名失败，请稍后重试。")
            input("\n按回车键退出...")
            return

        email = generate_random_email(domains)

        print("\n📝 请在 Augment Code 注册页面输入上述邮箱并完成人机验证")
        print("🤖 程序将自动监听并获取验证码")

        print("\n" + "-" * 100 + "\n")

        # 步骤 6: 验证码自动获取
        verification_code = get_verification_code(code, email)

        if verification_code:
            print("\n🎉 Augment Code 注册流程完成！")
            print("💡 您现在可以使用获取到的验证码完成注册。")
        else:
            print("\n❌ 验证码获取失败，请稍后重试或联系技术支持。")

    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作，程序退出。")
    except Exception as e:
        print(f"\n❌ 程序运行时发生未知错误: {str(e)}")
        print("💡 请联系技术支持或重新启动程序。")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n程序发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())
    finally:
        # 确保程序在任何情况下都会等待用户按回车键退出
        try:
            # 添加代售意向提示
            print("\n" + "=" * 100)
            print("💰 如果您有代售意向，请访问: https://aug.cursorx.pw/rebate.html")
            print("=" * 100)
            input("\n程序已结束，按回车键退出...")
        except:
            pass
