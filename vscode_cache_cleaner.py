#!/usr/bin/env python3
"""
VSCode 缓存清理工具

此脚本清理 Windows、macOS 和 Linux 上的 VSCode 缓存文件夹。
它模拟了通过 VSCode 命令面板提供的缓存清理功能。
还可以关闭所有正在运行的 VSCode 进程。
"""

import os
import sys
import shutil
import platform
import logging
import subprocess
import time
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    encoding='utf-8'  # 使用 UTF-8 编码
)
logger = logging.getLogger('vscode_cache_cleaner')

def kill_ide_processes(selected_apps=None):
    """
    关闭所有正在运行的 IDE 进程。

    参数:
        selected_apps (list): 要关闭的应用列表，可选值: ['vscode', 'cursor', 'idea', 'pycharm']
                             如果为 None，则只关闭 VSCode

    返回:
        bool: 如果成功关闭所有进程则为 True，否则为 False
    """
    if selected_apps is None:
        selected_apps = ['vscode']

    system = platform.system()
    success = True

    # 定义各应用的进程名称
    app_processes = {
        'vscode': {
            'Windows': ["code.exe", "Code.exe", "VSCode.exe", "Code - Insiders.exe"],
            'Darwin': ["Visual Studio Code"],
            'Linux': ["code"]
        },
        'cursor': {
            'Windows': ["Cursor.exe", "cursor.exe"],
            'Darwin': ["Cursor"],
            'Linux': ["cursor"]
        },
        'idea': {
            'Windows': ["idea64.exe", "idea.exe"],
            'Darwin': ["IntelliJ IDEA"],
            'Linux': ["idea"]
        },
        'pycharm': {
            'Windows': ["pycharm64.exe", "pycharm.exe"],
            'Darwin': ["PyCharm"],
            'Linux': ["pycharm"]
        }
    }

    # 构建要关闭的进程列表
    processes_to_kill = []
    app_names = []
    for app in selected_apps:
        if app in app_processes and system in app_processes[app]:
            processes_to_kill.extend(app_processes[app][system])
            app_names.append(app.upper())

    if not processes_to_kill:
        logger.warning("没有找到要关闭的进程")
        return True

    logger.info(f"正在关闭 {', '.join(app_names)} 进程...")

    try:
        if system == "Windows":

            for process in processes_to_kill:
                try:
                    # 使用 /F 强制终止进程，/T 终止子进程
                    subprocess.run(["taskkill", "/F", "/T", "/IM", process],
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                  check=False, encoding='utf-8', errors='replace')
                except Exception as e:
                    logger.warning(f"尝试终止 {process} 时出错: {e}")

            # 等待进程完全关闭
            time.sleep(2)

            # 检查是否还有 VSCode 进程在运行
            for process in processes_to_kill:
                result = subprocess.run(["tasklist", "/FI", f"IMAGENAME eq {process}"],
                                      stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                      check=False, encoding='utf-8', errors='replace')
                if process in result.stdout:
                    logger.warning(f"无法关闭所有 {process} 进程。请手动关闭 VSCode。")
                    success = False
                    break

            if success:
                logger.info(f"所有 {', '.join(app_names)} 进程已关闭。")

        elif system == "Darwin":  # macOS
            # 在 macOS 上使用 pkill 命令关闭进程
            for process in processes_to_kill:
                subprocess.run(["pkill", "-f", process],
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              check=False, encoding='utf-8', errors='replace')

            time.sleep(1)  # 等待进程关闭

            # 检查进程是否已关闭
            for process in processes_to_kill:
                result = subprocess.run(["pgrep", "-f", process],
                                       stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                       check=False, encoding='utf-8', errors='replace')
                if result.returncode == 0:
                    logger.warning(f"无法关闭所有 {process} 进程。请手动关闭相关应用。")
                    success = False

            if success:
                logger.info(f"所有 {', '.join(app_names)} 进程已关闭。")

        elif system == "Linux":
            # 在 Linux 上使用 pkill 命令关闭进程
            for process in processes_to_kill:
                subprocess.run(["pkill", "-f", process],
                              stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                              check=False, encoding='utf-8', errors='replace')

            time.sleep(1)  # 等待进程关闭

            # 检查进程是否已关闭
            for process in processes_to_kill:
                result = subprocess.run(["pgrep", "-f", process],
                                       stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                       check=False, encoding='utf-8', errors='replace')
                if result.returncode == 0:
                    logger.warning(f"无法关闭所有 {process} 进程。请手动关闭相关应用。")
                    success = False

            if success:
                logger.info(f"所有 {', '.join(app_names)} 进程已关闭。")

        else:
            logger.error(f"不支持的操作系统: {system}")
            success = False

    except Exception as e:
        logger.error(f"关闭 VSCode 进程时出错: {e}")
        success = False

    return success

# 向后兼容性函数
def kill_vscode_processes(include_cursor=False):
    """向后兼容的函数，调用新的 kill_ide_processes 函数"""
    selected_apps = ['vscode']
    if include_cursor:
        selected_apps.append('cursor')
    return kill_ide_processes(selected_apps)

def get_vscode_cache_paths(include_cursor=False):
    """向后兼容的函数，调用新的 get_ide_cache_paths 函数"""
    selected_apps = ['vscode']
    if include_cursor:
        selected_apps.append('cursor')
    return get_ide_cache_paths(selected_apps)

def get_ide_cache_paths(selected_apps=None):
    """
    根据操作系统获取选定 IDE 的缓存路径。

    参数:
        selected_apps (list): 要清理的应用列表，可选值: ['vscode', 'cursor', 'idea', 'pycharm']
                             如果为 None，则只获取 VSCode 缓存路径

    返回:
        dict: 包含不同缓存文件夹路径的字典
    """
    if selected_apps is None:
        selected_apps = ['vscode']

    system = platform.system()
    cache_paths = {}

    # 定义各应用的基础路径
    if system == "Windows":
        appdata = os.environ.get('APPDATA', '')
        localappdata = os.environ.get('LOCALAPPDATA', '')
        app_base_paths = {
            'vscode': Path(appdata) / "Code",
            'cursor': Path(appdata) / "Cursor",
            'idea': Path(localappdata) / "JetBrains" / "IntelliJIdea2023.3",  # 可能需要根据版本调整
            'pycharm': Path(localappdata) / "JetBrains" / "PyCharm2023.3"  # 可能需要根据版本调整
        }
    elif system == "Darwin":  # macOS
        app_base_paths = {
            'vscode': Path.home() / "Library" / "Application Support" / "Code",
            'cursor': Path.home() / "Library" / "Application Support" / "Cursor",
            'idea': Path.home() / "Library" / "Caches" / "JetBrains" / "IntelliJIdea2023.3",
            'pycharm': Path.home() / "Library" / "Caches" / "JetBrains" / "PyCharm2023.3"
        }
    elif system == "Linux":
        app_base_paths = {
            'vscode': Path.home() / ".config" / "Code",
            'cursor': Path.home() / ".config" / "Cursor",
            'idea': Path.home() / ".cache" / "JetBrains" / "IntelliJIdea2023.3",
            'pycharm': Path.home() / ".cache" / "JetBrains" / "PyCharm2023.3"
        }
    else:
        logger.error(f"不支持的操作系统: {system}")
        sys.exit(1)

    # 定义各应用的缓存子目录
    cache_subdirs = {
        'vscode': ["Cache", "CachedData", "CachedExtensions", "CachedExtensionVSIXs", "Code Cache", "GPUCache", "logs"],
        'cursor': ["Cache", "CachedData", "CachedExtensions", "CachedExtensionVSIXs", "Code Cache", "GPUCache", "logs"],
        'idea': ["log", "system", "tmp"],
        'pycharm': ["log", "system", "tmp"]
    }

    # 为选定的应用添加缓存路径
    for app in selected_apps:
        if app in app_base_paths and app in cache_subdirs:
            base_path = app_base_paths[app]
            app_name = app.upper()

            for subdir in cache_subdirs[app]:
                cache_paths[f"{app_name} {subdir}"] = base_path / subdir

    return cache_paths

def create_backup(path, backup_dir):
    """
    创建指定路径的备份。

    参数:
        path (Path): 要备份的路径
        backup_dir (Path): 存储备份的目录

    返回:
        bool: 如果备份成功则为 True，否则为 False
    """
    if not path.exists():
        return False

    try:
        # 如果备份目录不存在，则创建
        backup_dir.mkdir(parents=True, exist_ok=True)

        # 创建带时间戳的备份名称
        backup_name = f"{path.name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        backup_path = backup_dir / backup_name

        # 复制目录到备份
        if path.is_dir():
            shutil.copytree(path, backup_path)
        else:
            shutil.copy2(path, backup_path)

        logger.info(f"已创建 {path} 的备份，位置: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"创建 {path} 的备份失败: {e}")
        return False

def clear_cache(path, backup=False, backup_dir=None):
    """
    清除指定路径的缓存。

    参数:
        path (Path): 要清除的路径
        backup (bool): 是否在清除前创建备份
        backup_dir (Path): 存储备份的目录

    返回:
        bool: 如果清除成功则为 True，否则为 False
    """
    if not path.exists():
        logger.info(f"路径不存在，跳过: {path}")
        return True

    try:
        if backup and backup_dir:
            create_backup(path, backup_dir)

        if path.is_dir():
            try:
                # 尝试使用系统命令清除目录内容（可能有更高权限）
                if platform.system() == "Windows":
                    # 在 Windows 上使用 rd 和 del 命令
                    for item in path.iterdir():
                        item_str = str(item)
                        if item.is_dir():
                            # 使用 /S /Q 参数递归删除目录及其内容，不提示确认
                            subprocess.run(["rd", "/S", "/Q", item_str],
                                          shell=True, check=False,
                                          stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                        else:
                            # 使用 /F /Q 参数强制删除文件，不提示确认
                            subprocess.run(["del", "/F", "/Q", item_str],
                                          shell=True, check=False,
                                          stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                else:
                    # 在 Unix 系统上使用 rm 命令
                    subprocess.run(["rm", "-rf", str(path) + "/*"],
                                  shell=True, check=False,
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                logger.info(f"已使用系统命令清除目录: {path}")
            except Exception as e:
                logger.warning(f"使用系统命令清除目录失败，尝试使用 Python 方法: {e}")
                # 回退到 Python 方法
                for item in path.iterdir():
                    try:
                        if item.is_dir():
                            shutil.rmtree(item, ignore_errors=True)
                        else:
                            item.unlink(missing_ok=True)
                    except Exception as e2:
                        logger.error(f"无法删除 {item}: {e2}")

                logger.info(f"已尝试使用 Python 方法清除目录: {path}")
        else:
            # 删除文件
            try:
                # 尝试使用系统命令删除文件
                if platform.system() == "Windows":
                    subprocess.run(["del", "/F", "/Q", str(path)],
                                  shell=True, check=False,
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                else:
                    subprocess.run(["rm", "-f", str(path)],
                                  shell=True, check=False,
                                  stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                logger.info(f"已使用系统命令删除文件: {path}")
            except Exception as e:
                logger.warning(f"使用系统命令删除文件失败，尝试使用 Python 方法: {e}")
                # 回退到 Python 方法
                path.unlink(missing_ok=True)
                logger.info(f"已尝试使用 Python 方法删除文件: {path}")

        return True
    except Exception as e:
        logger.error(f"清除 {path} 失败: {e}")
        return False

def main(selected_apps=None):
    """
    清除选定 IDE 缓存的主函数。

    参数:
        selected_apps (list): 要清理的应用列表，可选值: ['vscode', 'cursor', 'idea', 'pycharm']
                             如果为 None，则只清理 VSCode
    """
    if selected_apps is None:
        selected_apps = ['vscode']

    try:
        # 解析命令行参数
        import argparse
        parser = argparse.ArgumentParser(description='清除 IDE 缓存文件夹')
        parser.add_argument('--backup', action='store_true', help='在清除缓存前创建备份')
        parser.add_argument('--backup-dir', type=str, help='存储备份的目录')
        parser.add_argument('--kill', action='store_true', help='关闭所有 IDE 进程')
        parser.add_argument('--cursor', action='store_true', help='同时清理 Cursor 缓存')
        parser.add_argument('--idea', action='store_true', help='同时清理 IntelliJ IDEA 缓存')
        parser.add_argument('--pycharm', action='store_true', help='同时清理 PyCharm 缓存')

        # 尝试解析参数，如果失败（例如在打包环境中），使用默认值
        try:
            args = parser.parse_args()
            # 根据参数更新选定的应用
            if args.cursor and 'cursor' not in selected_apps:
                selected_apps.append('cursor')
            if args.idea and 'idea' not in selected_apps:
                selected_apps.append('idea')
            if args.pycharm and 'pycharm' not in selected_apps:
                selected_apps.append('pycharm')
        except SystemExit:
            # 在打包环境中可能无法正确解析参数，使用默认值
            logger.warning("无法解析命令行参数，使用默认值")
            class DefaultArgs:
                backup = False
                backup_dir = None
                kill = True  # 默认关闭进程
                cursor = 'cursor' in selected_apps
                idea = 'idea' in selected_apps
                pycharm = 'pycharm' in selected_apps
            args = DefaultArgs()

        # 显示当前运行环境信息
        logger.info(f"操作系统: {platform.system()} {platform.release()}")
        logger.info(f"Python 版本: {platform.python_version()}")
        logger.info(f"是否在打包环境中运行: {getattr(sys, 'frozen', False)}")

        # 检查权限状态
        if platform.system() == "Windows":
            import ctypes
            if not ctypes.windll.shell32.IsUserAnAdmin():
                logger.info("以普通用户权限运行，将尽力清理可访问的缓存文件")

        # 如果指定了关闭进程
        if args.kill:
            app_names = [app.upper() for app in selected_apps]
            logger.info(f"准备关闭所有 {', '.join(app_names)} 进程...")
            kill_ide_processes(selected_apps)
            # 额外等待一段时间，确保进程完全关闭
            time.sleep(2)

        # 获取缓存路径
        cache_paths = get_ide_cache_paths(selected_apps)
        logger.info(f"找到 {len(cache_paths)} 个缓存路径")

        # 设置备份目录
        backup_dir = None
        if args.backup:
            if args.backup_dir:
                backup_dir = Path(args.backup_dir)
            else:
                backup_dir = Path.home() / "ide_cache_backup"

        # 清除每个缓存目录
        success_count = 0
        total_paths = len(cache_paths)

        app_names = [app.upper() for app in selected_apps]
        logger.info(f"开始在 {platform.system()} 上清理 {', '.join(app_names)} 缓存")

        for name, path in cache_paths.items():
            logger.info(f"正在处理 {name}，位置: {path}")
            try:
                if clear_cache(path, args.backup, backup_dir):
                    success_count += 1
            except Exception as e:
                logger.error(f"处理 {name} 时出错: {e}")

        # 打印摘要
        logger.info(f"缓存清理完成: {success_count}/{total_paths} 个位置已成功处理")

        # 提醒用户重启应用
        app_names = [app.upper() for app in selected_apps]
        logger.info(f"请重启 {', '.join(app_names)} 以使更改生效")

        return success_count > 0  # 如果至少清理了一个位置，则返回成功

    except Exception as e:
        logger.error(f"清理缓存时发生未处理的异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    main()
