#!/usr/bin/env python3
"""
测试 GUI 应用程序
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from aug_client_gui import main
    print("✅ 成功导入 aug_client_gui")
    
    # 检查依赖
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 可用")
    except ImportError:
        print("❌ PyQt5 未安装")
        sys.exit(1)
    
    try:
        from aug_client import CURRENT_VERSION
        print(f"✅ aug_client 模块可用，版本: {CURRENT_VERSION}")
    except ImportError as e:
        print(f"❌ aug_client 模块导入失败: {e}")
        sys.exit(1)
    
    print("🚀 启动 GUI 应用程序...")
    main()
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
