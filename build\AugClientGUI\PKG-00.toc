('D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\AugmentNew_2.0.0.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-disable-windowed-traceback', '', 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('aug_client_gui',
   'D:\\Temp Project\\Vscode Tarsh\\aug_client_gui.py',
   'PYSOURCE'),
  ('python313.dll', 'C:\\Program Files\\Python313\\python313.dll', 'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('zstandard\\_cffi.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zstandard\\_cffi.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\zstandard\\backend_c.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python313\\python3.dll', 'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('aug.env', 'D:\\Temp Project\\Vscode Tarsh\\aug.env', 'DATA'),
  ('aug_client.py', 'D:\\Temp Project\\Vscode Tarsh\\aug_client.py', 'DATA'),
  ('favicon.ico', 'D:\\Temp Project\\Vscode Tarsh\\favicon.ico', 'DATA'),
  ('first_use.txt', 'D:\\Temp Project\\Vscode Tarsh\\first_use.txt', 'DATA'),
  ('imap_verification_api.py',
   'D:\\Temp Project\\Vscode Tarsh\\imap_verification_api.py',
   'DATA'),
  ('registered_accounts.json',
   'D:\\Temp Project\\Vscode Tarsh\\registered_accounts.json',
   'DATA'),
  ('requirements.txt',
   'D:\\Temp Project\\Vscode Tarsh\\requirements.txt',
   'DATA'),
  ('solution.txt', 'D:\\Temp Project\\Vscode Tarsh\\solution.txt', 'DATA'),
  ('usage.json', 'D:\\Temp Project\\Vscode Tarsh\\usage.json', 'DATA'),
  ('version.txt', 'D:\\Temp Project\\Vscode Tarsh\\version.txt', 'DATA'),
  ('vscode_cache_cleaner.py',
   'D:\\Temp Project\\Vscode Tarsh\\vscode_cache_cleaner.py',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('pkgutil.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\pkgutil.pyc',
   'DATA'),
  ('zipimport.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\zipimport.pyc',
   'DATA'),
  ('struct.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\struct.pyc',
   'DATA'),
  ('importlib\\readers.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\readers.pyc',
   'DATA'),
  ('importlib\\resources\\readers.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\resources\\readers.pyc',
   'DATA'),
  ('importlib\\resources\\_itertools.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\resources\\_itertools.pyc',
   'DATA'),
  ('importlib\\resources\\abc.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\resources\\abc.pyc',
   'DATA'),
  ('typing.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\typing.pyc',
   'DATA'),
  ('importlib\\resources\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\resources\\__init__.pyc',
   'DATA'),
  ('importlib\\resources\\_functional.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\resources\\_functional.pyc',
   'DATA'),
  ('importlib\\resources\\_common.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\resources\\_common.pyc',
   'DATA'),
  ('importlib\\resources\\_adapters.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\resources\\_adapters.pyc',
   'DATA'),
  ('tempfile.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\tempfile.pyc',
   'DATA'),
  ('random.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\random.pyc',
   'DATA'),
  ('argparse.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\argparse.pyc',
   'DATA'),
  ('textwrap.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\textwrap.pyc',
   'DATA'),
  ('copy.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\copy.pyc',
   'DATA'),
  ('gettext.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\gettext.pyc',
   'DATA'),
  ('statistics.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\statistics.pyc',
   'DATA'),
  ('decimal.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\decimal.pyc',
   'DATA'),
  ('_pydecimal.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_pydecimal.pyc',
   'DATA'),
  ('contextvars.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\contextvars.pyc',
   'DATA'),
  ('fractions.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\fractions.pyc',
   'DATA'),
  ('numbers.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\numbers.pyc',
   'DATA'),
  ('hashlib.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\hashlib.pyc',
   'DATA'),
  ('logging\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\logging\\__init__.pyc',
   'DATA'),
  ('pickle.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\pickle.pyc',
   'DATA'),
  ('pprint.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\pprint.pyc',
   'DATA'),
  ('dataclasses.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\dataclasses.pyc',
   'DATA'),
  ('_compat_pickle.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_compat_pickle.pyc',
   'DATA'),
  ('string.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\string.pyc',
   'DATA'),
  ('bisect.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\bisect.pyc',
   'DATA'),
  ('zipfile\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\zipfile\\__init__.pyc',
   'DATA'),
  ('zipfile\\_path\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\zipfile\\_path\\__init__.pyc',
   'DATA'),
  ('zipfile\\_path\\glob.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\zipfile\\_path\\glob.pyc',
   'DATA'),
  ('py_compile.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\py_compile.pyc',
   'DATA'),
  ('importlib\\_bootstrap_external.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\_bootstrap_external.pyc',
   'DATA'),
  ('importlib\\metadata\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\metadata\\__init__.pyc',
   'DATA'),
  ('csv.pyc',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\0\\csv.pyc',
   'DATA'),
  ('importlib\\metadata\\_adapters.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\metadata\\_adapters.pyc',
   'DATA'),
  ('importlib\\metadata\\_text.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\metadata\\_text.pyc',
   'DATA'),
  ('email\\message.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\message.pyc',
   'DATA'),
  ('email\\policy.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\policy.pyc',
   'DATA'),
  ('email\\contentmanager.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\contentmanager.pyc',
   'DATA'),
  ('email\\quoprimime.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\quoprimime.pyc',
   'DATA'),
  ('email\\headerregistry.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\headerregistry.pyc',
   'DATA'),
  ('email\\_header_value_parser.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\_header_value_parser.pyc',
   'DATA'),
  ('urllib\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib\\__init__.pyc',
   'DATA'),
  ('email\\iterators.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\iterators.pyc',
   'DATA'),
  ('email\\generator.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\generator.pyc',
   'DATA'),
  ('email\\_encoded_words.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\_encoded_words.pyc',
   'DATA'),
  ('base64.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\base64.pyc',
   'DATA'),
  ('getopt.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\getopt.pyc',
   'DATA'),
  ('email\\charset.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\charset.pyc',
   'DATA'),
  ('email\\encoders.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\encoders.pyc',
   'DATA'),
  ('email\\base64mime.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\base64mime.pyc',
   'DATA'),
  ('email\\_policybase.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\_policybase.pyc',
   'DATA'),
  ('email\\errors.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\errors.pyc',
   'DATA'),
  ('email\\utils.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\utils.pyc',
   'DATA'),
  ('socket.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\socket.pyc',
   'DATA'),
  ('selectors.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\selectors.pyc',
   'DATA'),
  ('email\\_parseaddr.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\_parseaddr.pyc',
   'DATA'),
  ('calendar.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\calendar.pyc',
   'DATA'),
  ('urllib\\parse.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib\\parse.pyc',
   'DATA'),
  ('ipaddress.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ipaddress.pyc',
   'DATA'),
  ('quopri.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\quopri.pyc',
   'DATA'),
  ('importlib\\abc.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\abc.pyc',
   'DATA'),
  ('importlib\\_abc.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\_abc.pyc',
   'DATA'),
  ('importlib\\_bootstrap.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\_bootstrap.pyc',
   'DATA'),
  ('importlib\\metadata\\_itertools.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\metadata\\_itertools.pyc',
   'DATA'),
  ('importlib\\metadata\\_functools.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\metadata\\_functools.pyc',
   'DATA'),
  ('importlib\\metadata\\_collections.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\metadata\\_collections.pyc',
   'DATA'),
  ('importlib\\metadata\\_meta.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\metadata\\_meta.pyc',
   'DATA'),
  ('email\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\__init__.pyc',
   'DATA'),
  ('email\\parser.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\parser.pyc',
   'DATA'),
  ('email\\feedparser.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\feedparser.pyc',
   'DATA'),
  ('tokenize.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\tokenize.pyc',
   'DATA'),
  ('token.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\token.pyc',
   'DATA'),
  ('lzma.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\lzma.pyc',
   'DATA'),
  ('_compression.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_compression.pyc',
   'DATA'),
  ('bz2.pyc',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\0\\bz2.pyc',
   'DATA'),
  ('pathlib\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\pathlib\\__init__.pyc',
   'DATA'),
  ('pathlib\\_local.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\pathlib\\_local.pyc',
   'DATA'),
  ('glob.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\glob.pyc',
   'DATA'),
  ('fnmatch.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\fnmatch.pyc',
   'DATA'),
  ('pathlib\\_abc.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\pathlib\\_abc.pyc',
   'DATA'),
  ('contextlib.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\contextlib.pyc',
   'DATA'),
  ('__future__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\__future__.pyc',
   'DATA'),
  ('inspect.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\inspect.pyc',
   'DATA'),
  ('dis.pyc',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\0\\dis.pyc',
   'DATA'),
  ('opcode.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\opcode.pyc',
   'DATA'),
  ('_opcode_metadata.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_opcode_metadata.pyc',
   'DATA'),
  ('importlib\\machinery.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\machinery.pyc',
   'DATA'),
  ('importlib\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\__init__.pyc',
   'DATA'),
  ('_pyi_rth_utils\\qt.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_pyi_rth_utils\\qt.pyc',
   'DATA'),
  ('_pyi_rth_utils\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_pyi_rth_utils\\__init__.pyc',
   'DATA'),
  ('email\\header.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\header.pyc',
   'DATA'),
  ('email\\mime\\text.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\mime\\text.pyc',
   'DATA'),
  ('email\\mime\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\mime\\__init__.pyc',
   'DATA'),
  ('email\\mime\\nonmultipart.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\mime\\nonmultipart.pyc',
   'DATA'),
  ('email\\mime\\base.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\email\\mime\\base.pyc',
   'DATA'),
  ('imaplib.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\imaplib.pyc',
   'DATA'),
  ('getpass.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\getpass.pyc',
   'DATA'),
  ('hmac.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\hmac.pyc',
   'DATA'),
  ('importlib\\util.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\importlib\\util.pyc',
   'DATA'),
  ('shutil.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\shutil.pyc',
   'DATA'),
  ('tarfile.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\tarfile.pyc',
   'DATA'),
  ('gzip.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\gzip.pyc',
   'DATA'),
  ('certifi\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\certifi\\__init__.pyc',
   'DATA'),
  ('certifi\\core.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\certifi\\core.pyc',
   'DATA'),
  ('ssl.pyc',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\0\\ssl.pyc',
   'DATA'),
  ('urllib3\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\__init__.pyc',
   'DATA'),
  ('urllib3\\contrib\\emscripten\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\contrib\\emscripten\\__init__.pyc',
   'DATA'),
  ('urllib3\\contrib\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\contrib\\__init__.pyc',
   'DATA'),
  ('urllib3\\contrib\\pyopenssl.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\contrib\\pyopenssl.pyc',
   'DATA'),
  ('idna\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\idna\\__init__.pyc',
   'DATA'),
  ('idna\\package_data.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\idna\\package_data.pyc',
   'DATA'),
  ('idna\\intranges.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\idna\\intranges.pyc',
   'DATA'),
  ('idna\\core.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\idna\\core.pyc',
   'DATA'),
  ('idna\\uts46data.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\idna\\uts46data.pyc',
   'DATA'),
  ('idna\\idnadata.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\idna\\idnadata.pyc',
   'DATA'),
  ('urllib3\\util\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\__init__.pyc',
   'DATA'),
  ('urllib3\\util\\wait.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\wait.pyc',
   'DATA'),
  ('urllib3\\util\\url.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\url.pyc',
   'DATA'),
  ('urllib3\\util\\util.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\util.pyc',
   'DATA'),
  ('urllib3\\util\\ssl_.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\ssl_.pyc',
   'DATA'),
  ('urllib3\\util\\ssltransport.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\ssltransport.pyc',
   'DATA'),
  ('urllib3\\util\\response.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\response.pyc',
   'DATA'),
  ('http\\client.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\http\\client.pyc',
   'DATA'),
  ('http\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\http\\__init__.pyc',
   'DATA'),
  ('http\\cookiejar.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\http\\cookiejar.pyc',
   'DATA'),
  ('urllib\\request.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib\\request.pyc',
   'DATA'),
  ('nturl2path.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\nturl2path.pyc',
   'DATA'),
  ('ftplib.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ftplib.pyc',
   'DATA'),
  ('netrc.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\netrc.pyc',
   'DATA'),
  ('mimetypes.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\mimetypes.pyc',
   'DATA'),
  ('urllib\\response.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib\\response.pyc',
   'DATA'),
  ('urllib\\error.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib\\error.pyc',
   'DATA'),
  ('urllib3\\util\\connection.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\connection.pyc',
   'DATA'),
  ('urllib3\\contrib\\emscripten\\connection.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\contrib\\emscripten\\connection.pyc',
   'DATA'),
  ('urllib3\\contrib\\emscripten\\response.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\contrib\\emscripten\\response.pyc',
   'DATA'),
  ('urllib3\\contrib\\emscripten\\request.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\contrib\\emscripten\\request.pyc',
   'DATA'),
  ('urllib3\\contrib\\emscripten\\fetch.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\contrib\\emscripten\\fetch.pyc',
   'DATA'),
  ('urllib3\\connection.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\connection.pyc',
   'DATA'),
  ('urllib3\\util\\ssl_match_hostname.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\ssl_match_hostname.pyc',
   'DATA'),
  ('urllib3\\http2\\probe.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\http2\\probe.pyc',
   'DATA'),
  ('urllib3\\http2\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\http2\\__init__.pyc',
   'DATA'),
  ('urllib3\\http2\\connection.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\http2\\connection.pyc',
   'DATA'),
  ('urllib3\\util\\timeout.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\timeout.pyc',
   'DATA'),
  ('urllib3\\util\\retry.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\retry.pyc',
   'DATA'),
  ('urllib3\\util\\request.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\request.pyc',
   'DATA'),
  ('zstandard\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\zstandard\\__init__.pyc',
   'DATA'),
  ('zstandard\\backend_cffi.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\zstandard\\backend_cffi.pyc',
   'DATA'),
  ('urllib3\\response.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\response.pyc',
   'DATA'),
  ('urllib3\\poolmanager.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\poolmanager.pyc',
   'DATA'),
  ('urllib3\\util\\proxy.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\util\\proxy.pyc',
   'DATA'),
  ('urllib3\\_request_methods.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\_request_methods.pyc',
   'DATA'),
  ('urllib3\\filepost.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\filepost.pyc',
   'DATA'),
  ('urllib3\\fields.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\fields.pyc',
   'DATA'),
  ('urllib3\\connectionpool.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\connectionpool.pyc',
   'DATA'),
  ('queue.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\queue.pyc',
   'DATA'),
  ('urllib3\\_version.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\_version.pyc',
   'DATA'),
  ('urllib3\\_collections.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\_collections.pyc',
   'DATA'),
  ('urllib3\\_base_connection.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\_base_connection.pyc',
   'DATA'),
  ('urllib3\\exceptions.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\exceptions.pyc',
   'DATA'),
  ('copyreg.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\copyreg.pyc',
   'DATA'),
  ('reprlib.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\reprlib.pyc',
   'DATA'),
  ('codecs.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\codecs.pyc',
   'DATA'),
  ('stat.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\stat.pyc',
   'DATA'),
  ('weakref.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\weakref.pyc',
   'DATA'),
  ('keyword.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\keyword.pyc',
   'DATA'),
  ('encodings\\zlib_codec.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\zlib_codec.pyc',
   'DATA'),
  ('encodings\\uu_codec.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\uu_codec.pyc',
   'DATA'),
  ('encodings\\utf_8_sig.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_8_sig.pyc',
   'DATA'),
  ('encodings\\utf_8.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_8.pyc',
   'DATA'),
  ('encodings\\utf_7.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_7.pyc',
   'DATA'),
  ('encodings\\utf_32_le.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_32_le.pyc',
   'DATA'),
  ('encodings\\utf_32_be.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_32_be.pyc',
   'DATA'),
  ('encodings\\utf_32.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_32.pyc',
   'DATA'),
  ('encodings\\utf_16_le.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_16_le.pyc',
   'DATA'),
  ('encodings\\utf_16_be.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_16_be.pyc',
   'DATA'),
  ('encodings\\utf_16.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\utf_16.pyc',
   'DATA'),
  ('encodings\\unicode_escape.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\unicode_escape.pyc',
   'DATA'),
  ('encodings\\undefined.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\undefined.pyc',
   'DATA'),
  ('encodings\\tis_620.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\tis_620.pyc',
   'DATA'),
  ('encodings\\shift_jisx0213.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\shift_jisx0213.pyc',
   'DATA'),
  ('encodings\\shift_jis_2004.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\shift_jis_2004.pyc',
   'DATA'),
  ('encodings\\shift_jis.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\shift_jis.pyc',
   'DATA'),
  ('encodings\\rot_13.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\rot_13.pyc',
   'DATA'),
  ('encodings\\raw_unicode_escape.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\raw_unicode_escape.pyc',
   'DATA'),
  ('encodings\\quopri_codec.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\quopri_codec.pyc',
   'DATA'),
  ('encodings\\punycode.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\punycode.pyc',
   'DATA'),
  ('encodings\\ptcp154.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\ptcp154.pyc',
   'DATA'),
  ('encodings\\palmos.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\palmos.pyc',
   'DATA'),
  ('encodings\\oem.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\oem.pyc',
   'DATA'),
  ('encodings\\mbcs.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mbcs.pyc',
   'DATA'),
  ('encodings\\mac_turkish.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_turkish.pyc',
   'DATA'),
  ('encodings\\mac_romanian.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_romanian.pyc',
   'DATA'),
  ('encodings\\mac_roman.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_roman.pyc',
   'DATA'),
  ('encodings\\mac_latin2.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_latin2.pyc',
   'DATA'),
  ('encodings\\mac_iceland.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_iceland.pyc',
   'DATA'),
  ('encodings\\mac_greek.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_greek.pyc',
   'DATA'),
  ('encodings\\mac_farsi.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_farsi.pyc',
   'DATA'),
  ('encodings\\mac_cyrillic.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_cyrillic.pyc',
   'DATA'),
  ('encodings\\mac_croatian.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_croatian.pyc',
   'DATA'),
  ('encodings\\mac_arabic.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\mac_arabic.pyc',
   'DATA'),
  ('encodings\\latin_1.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\latin_1.pyc',
   'DATA'),
  ('encodings\\kz1048.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\kz1048.pyc',
   'DATA'),
  ('encodings\\koi8_u.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\koi8_u.pyc',
   'DATA'),
  ('encodings\\koi8_t.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\koi8_t.pyc',
   'DATA'),
  ('encodings\\koi8_r.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\koi8_r.pyc',
   'DATA'),
  ('encodings\\johab.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\johab.pyc',
   'DATA'),
  ('encodings\\iso8859_9.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_9.pyc',
   'DATA'),
  ('encodings\\iso8859_8.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_8.pyc',
   'DATA'),
  ('encodings\\iso8859_7.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_7.pyc',
   'DATA'),
  ('encodings\\iso8859_6.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_6.pyc',
   'DATA'),
  ('encodings\\iso8859_5.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_5.pyc',
   'DATA'),
  ('encodings\\iso8859_4.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_4.pyc',
   'DATA'),
  ('encodings\\iso8859_3.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_3.pyc',
   'DATA'),
  ('encodings\\iso8859_2.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_2.pyc',
   'DATA'),
  ('encodings\\iso8859_16.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_16.pyc',
   'DATA'),
  ('encodings\\iso8859_15.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_15.pyc',
   'DATA'),
  ('encodings\\iso8859_14.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_14.pyc',
   'DATA'),
  ('encodings\\iso8859_13.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_13.pyc',
   'DATA'),
  ('encodings\\iso8859_11.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_11.pyc',
   'DATA'),
  ('encodings\\iso8859_10.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_10.pyc',
   'DATA'),
  ('encodings\\iso8859_1.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso8859_1.pyc',
   'DATA'),
  ('encodings\\iso2022_kr.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso2022_kr.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_ext.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso2022_jp_ext.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_3.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso2022_jp_3.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_2004.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso2022_jp_2004.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_2.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso2022_jp_2.pyc',
   'DATA'),
  ('encodings\\iso2022_jp_1.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso2022_jp_1.pyc',
   'DATA'),
  ('encodings\\iso2022_jp.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\iso2022_jp.pyc',
   'DATA'),
  ('encodings\\idna.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\idna.pyc',
   'DATA'),
  ('stringprep.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\stringprep.pyc',
   'DATA'),
  ('encodings\\hz.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\hz.pyc',
   'DATA'),
  ('encodings\\hp_roman8.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\hp_roman8.pyc',
   'DATA'),
  ('encodings\\hex_codec.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\hex_codec.pyc',
   'DATA'),
  ('encodings\\gbk.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\gbk.pyc',
   'DATA'),
  ('encodings\\gb2312.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\gb2312.pyc',
   'DATA'),
  ('encodings\\gb18030.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\gb18030.pyc',
   'DATA'),
  ('encodings\\euc_kr.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\euc_kr.pyc',
   'DATA'),
  ('encodings\\euc_jp.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\euc_jp.pyc',
   'DATA'),
  ('encodings\\euc_jisx0213.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\euc_jisx0213.pyc',
   'DATA'),
  ('encodings\\euc_jis_2004.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\euc_jis_2004.pyc',
   'DATA'),
  ('encodings\\cp950.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp950.pyc',
   'DATA'),
  ('encodings\\cp949.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp949.pyc',
   'DATA'),
  ('encodings\\cp932.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp932.pyc',
   'DATA'),
  ('encodings\\cp875.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp875.pyc',
   'DATA'),
  ('encodings\\cp874.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp874.pyc',
   'DATA'),
  ('encodings\\cp869.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp869.pyc',
   'DATA'),
  ('encodings\\cp866.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp866.pyc',
   'DATA'),
  ('encodings\\cp865.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp865.pyc',
   'DATA'),
  ('encodings\\cp864.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp864.pyc',
   'DATA'),
  ('encodings\\cp863.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp863.pyc',
   'DATA'),
  ('encodings\\cp862.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp862.pyc',
   'DATA'),
  ('encodings\\cp861.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp861.pyc',
   'DATA'),
  ('encodings\\cp860.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp860.pyc',
   'DATA'),
  ('encodings\\cp858.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp858.pyc',
   'DATA'),
  ('encodings\\cp857.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp857.pyc',
   'DATA'),
  ('encodings\\cp856.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp856.pyc',
   'DATA'),
  ('encodings\\cp855.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp855.pyc',
   'DATA'),
  ('encodings\\cp852.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp852.pyc',
   'DATA'),
  ('encodings\\cp850.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp850.pyc',
   'DATA'),
  ('encodings\\cp775.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp775.pyc',
   'DATA'),
  ('encodings\\cp737.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp737.pyc',
   'DATA'),
  ('encodings\\cp720.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp720.pyc',
   'DATA'),
  ('encodings\\cp500.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp500.pyc',
   'DATA'),
  ('encodings\\cp437.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp437.pyc',
   'DATA'),
  ('encodings\\cp424.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp424.pyc',
   'DATA'),
  ('encodings\\cp273.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp273.pyc',
   'DATA'),
  ('encodings\\cp1258.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1258.pyc',
   'DATA'),
  ('encodings\\cp1257.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1257.pyc',
   'DATA'),
  ('encodings\\cp1256.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1256.pyc',
   'DATA'),
  ('encodings\\cp1255.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1255.pyc',
   'DATA'),
  ('encodings\\cp1254.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1254.pyc',
   'DATA'),
  ('encodings\\cp1253.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1253.pyc',
   'DATA'),
  ('encodings\\cp1252.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1252.pyc',
   'DATA'),
  ('encodings\\cp1251.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1251.pyc',
   'DATA'),
  ('encodings\\cp1250.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1250.pyc',
   'DATA'),
  ('encodings\\cp1140.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1140.pyc',
   'DATA'),
  ('encodings\\cp1125.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1125.pyc',
   'DATA'),
  ('encodings\\cp1026.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1026.pyc',
   'DATA'),
  ('encodings\\cp1006.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp1006.pyc',
   'DATA'),
  ('encodings\\cp037.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\cp037.pyc',
   'DATA'),
  ('encodings\\charmap.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\charmap.pyc',
   'DATA'),
  ('encodings\\bz2_codec.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\bz2_codec.pyc',
   'DATA'),
  ('encodings\\big5hkscs.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\big5hkscs.pyc',
   'DATA'),
  ('encodings\\big5.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\big5.pyc',
   'DATA'),
  ('encodings\\base64_codec.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\base64_codec.pyc',
   'DATA'),
  ('encodings\\ascii.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\ascii.pyc',
   'DATA'),
  ('encodings\\aliases.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\aliases.pyc',
   'DATA'),
  ('encodings\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\encodings\\__init__.pyc',
   'DATA'),
  ('locale.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\locale.pyc',
   'DATA'),
  ('sre_constants.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\sre_constants.pyc',
   'DATA'),
  ('functools.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\functools.pyc',
   'DATA'),
  ('_weakrefset.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_weakrefset.pyc',
   'DATA'),
  ('heapq.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\heapq.pyc',
   'DATA'),
  ('traceback.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\traceback.pyc',
   'DATA'),
  ('_colorize.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_colorize.pyc',
   'DATA'),
  ('operator.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\operator.pyc',
   'DATA'),
  ('enum.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\enum.pyc',
   'DATA'),
  ('sre_compile.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\sre_compile.pyc',
   'DATA'),
  ('abc.pyc',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\0\\abc.pyc',
   'DATA'),
  ('_py_abc.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_py_abc.pyc',
   'DATA'),
  ('posixpath.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\posixpath.pyc',
   'DATA'),
  ('types.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\types.pyc',
   'DATA'),
  ('re\\_parser.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\re\\_parser.pyc',
   'DATA'),
  ('re\\_constants.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\re\\_constants.pyc',
   'DATA'),
  ('re\\_compiler.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\re\\_compiler.pyc',
   'DATA'),
  ('re\\_casefix.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\re\\_casefix.pyc',
   'DATA'),
  ('re\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\re\\__init__.pyc',
   'DATA'),
  ('genericpath.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\genericpath.pyc',
   'DATA'),
  ('_collections_abc.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_collections_abc.pyc',
   'DATA'),
  ('collections\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\collections\\__init__.pyc',
   'DATA'),
  ('sre_parse.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\sre_parse.pyc',
   'DATA'),
  ('linecache.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\linecache.pyc',
   'DATA'),
  ('io.pyc',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\0\\io.pyc',
   'DATA'),
  ('warnings.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\warnings.pyc',
   'DATA'),
  ('tracemalloc.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\tracemalloc.pyc',
   'DATA'),
  ('ntpath.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ntpath.pyc',
   'DATA'),
  ('json\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\json\\__init__.pyc',
   'DATA'),
  ('json\\encoder.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\json\\encoder.pyc',
   'DATA'),
  ('json\\decoder.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\json\\decoder.pyc',
   'DATA'),
  ('json\\scanner.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\json\\scanner.pyc',
   'DATA'),
  ('datetime.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\datetime.pyc',
   'DATA'),
  ('_pydatetime.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_pydatetime.pyc',
   'DATA'),
  ('_strptime.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_strptime.pyc',
   'DATA'),
  ('platform.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\platform.pyc',
   'DATA'),
  ('ctypes\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ctypes\\__init__.pyc',
   'DATA'),
  ('ctypes\\util.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ctypes\\util.pyc',
   'DATA'),
  ('ctypes\\_aix.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ctypes\\_aix.pyc',
   'DATA'),
  ('ctypes\\macholib\\dyld.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ctypes\\macholib\\dyld.pyc',
   'DATA'),
  ('ctypes\\macholib\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ctypes\\macholib\\__init__.pyc',
   'DATA'),
  ('ctypes\\macholib\\dylib.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ctypes\\macholib\\dylib.pyc',
   'DATA'),
  ('ctypes\\macholib\\framework.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ctypes\\macholib\\framework.pyc',
   'DATA'),
  ('ctypes\\_endian.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\ctypes\\_endian.pyc',
   'DATA'),
  ('_ios_support.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_ios_support.pyc',
   'DATA'),
  ('subprocess.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\subprocess.pyc',
   'DATA'),
  ('signal.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\signal.pyc',
   'DATA'),
  ('ast.pyc',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\0\\ast.pyc',
   'DATA'),
  ('packaging\\version.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\packaging\\version.pyc',
   'DATA'),
  ('packaging\\_structures.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\packaging\\_structures.pyc',
   'DATA'),
  ('packaging\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\packaging\\__init__.pyc',
   'DATA'),
  ('requests\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\__init__.pyc',
   'DATA'),
  ('requests\\status_codes.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\status_codes.pyc',
   'DATA'),
  ('requests\\structures.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\structures.pyc',
   'DATA'),
  ('requests\\compat.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\compat.pyc',
   'DATA'),
  ('http\\cookies.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\http\\cookies.pyc',
   'DATA'),
  ('requests\\models.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\models.pyc',
   'DATA'),
  ('requests\\hooks.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\hooks.pyc',
   'DATA'),
  ('requests\\cookies.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\cookies.pyc',
   'DATA'),
  ('requests\\auth.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\auth.pyc',
   'DATA'),
  ('requests\\_internal_utils.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\_internal_utils.pyc',
   'DATA'),
  ('requests\\api.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\api.pyc',
   'DATA'),
  ('requests\\sessions.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\sessions.pyc',
   'DATA'),
  ('requests\\adapters.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\adapters.pyc',
   'DATA'),
  ('urllib3\\contrib\\socks.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\urllib3\\contrib\\socks.pyc',
   'DATA'),
  ('socks.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\socks.pyc',
   'DATA'),
  ('requests\\__version__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\__version__.pyc',
   'DATA'),
  ('requests\\utils.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\utils.pyc',
   'DATA'),
  ('requests\\certs.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\certs.pyc',
   'DATA'),
  ('requests\\packages.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\packages.pyc',
   'DATA'),
  ('charset_normalizer\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\charset_normalizer\\__init__.pyc',
   'DATA'),
  ('charset_normalizer\\version.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\charset_normalizer\\version.pyc',
   'DATA'),
  ('charset_normalizer\\utils.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\charset_normalizer\\utils.pyc',
   'DATA'),
  ('charset_normalizer\\constant.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\charset_normalizer\\constant.pyc',
   'DATA'),
  ('charset_normalizer\\models.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\charset_normalizer\\models.pyc',
   'DATA'),
  ('charset_normalizer\\cd.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\charset_normalizer\\cd.pyc',
   'DATA'),
  ('charset_normalizer\\legacy.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\charset_normalizer\\legacy.pyc',
   'DATA'),
  ('charset_normalizer\\api.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\charset_normalizer\\api.pyc',
   'DATA'),
  ('requests\\exceptions.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\requests\\exceptions.pyc',
   'DATA'),
  ('aug_client.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\aug_client.pyc',
   'DATA'),
  ('dotenv\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\dotenv\\__init__.pyc',
   'DATA'),
  ('dotenv\\ipython.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\dotenv\\ipython.pyc',
   'DATA'),
  ('dotenv\\main.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\dotenv\\main.pyc',
   'DATA'),
  ('dotenv\\variables.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\dotenv\\variables.pyc',
   'DATA'),
  ('dotenv\\parser.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\dotenv\\parser.pyc',
   'DATA'),
  ('PyQt5\\__init__.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\PyQt5\\__init__.pyc',
   'DATA'),
  ('webbrowser.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\webbrowser.pyc',
   'DATA'),
  ('shlex.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\shlex.pyc',
   'DATA'),
  ('threading.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\threading.pyc',
   'DATA'),
  ('_threading_local.pyc',
   'D:\\Temp Project\\Vscode '
   'Tarsh\\build\\AugClientGUI\\localpycs\\0\\_threading_local.pyc',
   'DATA'),
  ('os.pyc',
   'D:\\Temp Project\\Vscode Tarsh\\build\\AugClientGUI\\localpycs\\0\\os.pyc',
   'DATA')],
 'python313.dll',
 False,
 True,
 False,
 [],
 None,
 None,
 None)
