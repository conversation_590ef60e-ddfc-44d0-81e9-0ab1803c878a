#!/usr/bin/env python3
"""
Augment Code 客户端 GUI 版本

基于 PyQt5 的图形界面版本，提供更友好的用户体验。
"""

import sys
import os
import threading
import webbrowser
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QTextEdit, QCheckBox, QGroupBox, QProgressBar,
                             QMessageBox, QSplitter, QFrame, QDialog, QDialogButtonBox,
                             QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon
from dotenv import load_dotenv

# 导入原有的功能模块
try:
    from aug_client import (
        check_version, verify_code, get_mail_domains, generate_random_email,
        get_verification_code, clean_vscode_cache, get_code_usage_info,
        ENV_FILE, CURRENT_VERSION, API_BASE_URL
    )
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保 aug_client.py 文件在同一目录下")
    sys.exit(1)


class AppSelectionDialog(QDialog):
    """应用选择对话框"""

    def __init__(self, parent=None, current_selection=None):
        super().__init__(parent)
        self.selected_apps = current_selection or []
        self.init_ui()

    def init_ui(self):
        """初始化对话框界面"""
        self.setWindowTitle("选择要清理缓存的应用")
        self.setModal(True)
        self.setFixedSize(500, 450)
        self.setStyleSheet(self.parent().get_modern_stylesheet() if self.parent() else "")

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🧹 选择要清理缓存的应用")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 2px solid #667eea;
            }
        """)
        layout.addWidget(title_label)

        # 说明文字
        info_label = QLabel("💡 请选择您要同时清理缓存的应用程序：")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                margin-bottom: 10px;
                background: transparent;
                border: none;
                padding: 0px;
            }
        """)
        layout.addWidget(info_label)

        # 应用选择列表
        self.app_list = QListWidget()
        self.app_list.setMinimumHeight(220)
        self.app_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
                font-size: 14px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f1f3f4;
            }
            QListWidget::item:hover {
                background-color: #f8f9fa;
            }
            QListWidget::item:selected {
                background-color: #667eea;
                color: white;
            }
        """)

        # 添加应用选项
        apps = [
            ("vscode", "🔵 Visual Studio Code", "微软开发的代码编辑器"),
            ("cursor", "🟡 Cursor", "AI 驱动的代码编辑器"),
            ("idea", "🔴 IntelliJ IDEA", "JetBrains 开发的 Java IDE"),
            ("pycharm", "🟢 PyCharm", "JetBrains 开发的 Python IDE")
        ]

        for app_id, app_name, app_desc in apps:
            item = QListWidgetItem(f"{app_name}\n{app_desc}")
            item.setData(Qt.UserRole, app_id)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)

            # 设置初始选中状态
            if app_id in self.selected_apps:
                item.setCheckState(Qt.Checked)
            else:
                item.setCheckState(Qt.Unchecked)

            self.app_list.addItem(item)

        layout.addWidget(self.app_list)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # 确认和取消按钮
        confirm_btn = QPushButton("确认")
        confirm_btn.clicked.connect(self.accept)
        confirm_btn.setMinimumHeight(35)
        confirm_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20c997);
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #218838, stop:1 #1e7e34);
            }
        """)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setMinimumHeight(35)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d62c1a, stop:1 #a93226);
            }
        """)

        button_layout.addWidget(confirm_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

    def get_selected_apps(self):
        """获取选中的应用列表"""
        selected = []
        for i in range(self.app_list.count()):
            item = self.app_list.item(i)
            if item.checkState() == Qt.Checked:
                app_id = item.data(Qt.UserRole)
                selected.append(app_id)
        return selected


class WorkerThread(QThread):
    """工作线程，用于执行耗时操作"""
    progress_update = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    step_completed = pyqtSignal(int, str)  # 步骤完成信号：步骤号，结果信息
    workflow_step = pyqtSignal(int, str)   # 工作流步骤信号：步骤号，步骤描述

    def __init__(self, task_type, *args, parent=None):
        super().__init__()
        self.task_type = task_type
        self.args = args
        self.main_window = parent

    def run(self):
        try:
            if self.task_type == "version_check":
                result = check_version()
                self.finished.emit(result, "版本检查完成")

            elif self.task_type == "code_verify":
                code = self.args[0]
                result = verify_code(code)
                self.finished.emit(result, "代码验证完成")

            elif self.task_type == "cache_clean":
                selected_apps = self.args[0] if self.args else ['vscode']
                result = clean_vscode_cache(selected_apps)
                self.finished.emit(result, "缓存清理完成")

            elif self.task_type == "get_email":
                domains = get_mail_domains()
                if domains:
                    email = generate_random_email(domains)
                    self.finished.emit(True, email)
                else:
                    self.finished.emit(False, "获取邮箱失败")

            elif self.task_type == "get_verification":
                code, email = self.args
                verification_code = get_verification_code(code, email)
                if verification_code:
                    self.finished.emit(True, verification_code)
                else:
                    self.finished.emit(False, "验证码获取失败")

            elif self.task_type == "one_click_workflow":
                self.run_one_click_workflow()

            elif self.task_type == "get_usage_info":
                code = self.args[0]
                usage_info = get_code_usage_info(code)
                if usage_info:
                    self.finished.emit(True, str(usage_info))
                else:
                    self.finished.emit(False, "获取使用信息失败")

        except Exception as e:
            self.finished.emit(False, f"操作失败: {str(e)}")

    def run_one_click_workflow(self):
        """执行一键式工作流"""
        try:
            # 步骤1: 清理缓存
            self.workflow_step.emit(1, "正在清理缓存...")
            # 从主窗口获取选定的应用
            selected_apps = getattr(self.main_window, 'selected_apps', ['vscode']) if self.main_window else ['vscode']
            result = clean_vscode_cache(selected_apps)
            if not result:
                self.step_completed.emit(1, "缓存清理失败")
                self.finished.emit(False, "工作流在步骤1失败：缓存清理失败")
                return
            self.step_completed.emit(1, "缓存清理完成")

            # 步骤2: 获取临时邮箱
            self.workflow_step.emit(2, "正在获取临时邮箱...")
            domains = get_mail_domains()
            if not domains:
                self.step_completed.emit(2, "获取邮箱失败")
                self.finished.emit(False, "工作流在步骤2失败：获取邮箱失败")
                return

            email = generate_random_email(domains)
            self.step_completed.emit(2, f"临时邮箱获取成功: {email}")

            # 步骤3: 等待用户确认
            self.workflow_step.emit(3, "等待用户确认获取验证码...")
            self.finished.emit(True, f"workflow_completed:{email}")

        except Exception as e:
            self.finished.emit(False, f"工作流执行失败: {str(e)}")


class VersionCheckThread(QThread):
    """专门用于版本检查的线程"""
    finished = pyqtSignal(bool, str)  # 成功状态，下载链接

    def run(self):
        try:
            import requests
            from packaging import version

            response = requests.get(f"{API_BASE_URL}/version", timeout=10)

            if response.status_code != 200:
                self.finished.emit(True, "")  # 网络错误时不阻止启动
                return

            data = response.json()

            if not data.get("status"):
                self.finished.emit(True, "")  # 服务器错误时不阻止启动
                return

            server_version = data.get("version")
            download_url = data.get("download_url", "")

            # 版本比较
            if version.parse(server_version) > version.parse(CURRENT_VERSION):
                self.finished.emit(False, download_url)  # 需要更新
            else:
                self.finished.emit(True, "")  # 版本最新

        except Exception:
            # 任何异常都不阻止程序启动
            self.finished.emit(True, "")


class VerificationListenerThread(QThread):
    """验证码监听线程"""
    verification_received = pyqtSignal(str)  # 收到验证码信号
    status_update = pyqtSignal(int)  # 状态更新信号：剩余时间
    finished = pyqtSignal(bool, str)  # 完成信号：成功状态，消息

    def __init__(self, access_code, email, max_duration=300):
        super().__init__()
        self.access_code = access_code
        self.email = email
        self.max_duration = max_duration
        self.should_stop = False

    def stop_listening(self):
        """停止监听"""
        self.should_stop = True

    def run(self):
        """运行监听"""
        import time

        start_time = time.time()

        while not self.should_stop:
            elapsed_time = time.time() - start_time
            remaining_time = max(0, self.max_duration - int(elapsed_time))

            # 发送状态更新
            self.status_update.emit(remaining_time)

            # 检查是否超时
            if remaining_time <= 0:
                self.finished.emit(False, "监听超时")
                return

            # 尝试获取验证码
            try:
                verification_code = get_verification_code(self.access_code, self.email)
                if verification_code:
                    self.verification_received.emit(verification_code)
                    # 不在这里发送 finished 信号，让主线程控制
                    return
            except Exception as e:
                # 静默处理异常，继续监听
                pass

            # 等待5秒后重试
            for i in range(50):  # 5秒 = 50 * 0.1秒
                if self.should_stop:
                    self.finished.emit(False, "用户停止监听")
                    return
                time.sleep(0.1)

        self.finished.emit(False, "用户停止监听")


class AugmentClientGUI(QMainWindow):
    """Augment Code 客户端主窗口"""

    def __init__(self):
        super().__init__()
        self.current_email = ""
        self.access_code = ""
        self.workflow_step_labels = []  # 工作流步骤标签
        self.current_step = 0  # 当前步骤
        self.selected_apps = ['vscode']  # 默认只选择 VSCode
        self.registered_accounts = []  # 已注册账号列表
        self.verification_worker = None  # 验证码监听工作线程
        self.verification_timer = None  # 验证码监听计时器
        self.current_listening_email = ""  # 当前正在监听的邮箱
        self.init_ui()
        self.load_config()

        # 启动时检查版本和验证访问代码
        self.startup_sequence()

    def startup_sequence(self):
        """启动序列：版本检查 → 访问代码验证"""
        # 先隐藏主窗口
        self.hide()

        # 开始版本检查
        self.check_version_on_startup()

    def check_version_on_startup(self):
        """启动时检查版本"""
        self.version_worker = VersionCheckThread()
        self.version_worker.finished.connect(self.on_startup_version_check_finished)
        self.version_worker.start()

    def on_startup_version_check_finished(self, success, download_url):
        """启动时版本检查完成回调"""
        if not success:
            # 版本检查失败，显示更新对话框
            self.show_update_dialog(download_url)
        else:
            # 版本检查通过，继续验证访问代码
            self.check_access_code_startup()

    def show_update_dialog(self, download_url):
        """显示版本更新对话框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("版本更新")
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setText("🆕 发现新版本可用！")
        msg_box.setInformativeText("为确保功能正常，建议更新到最新版本。\n\n点击'确认'打开下载页面，点击'取消'退出程序。")

        # 设置按钮
        confirm_btn = msg_box.addButton("确认", QMessageBox.AcceptRole)
        msg_box.addButton("取消", QMessageBox.RejectRole)

        # 设置默认按钮
        msg_box.setDefaultButton(confirm_btn)

        # 显示对话框并处理结果
        msg_box.exec_()

        if msg_box.clickedButton() == confirm_btn:
            # 打开浏览器下载页面
            try:
                if download_url:
                    webbrowser.open(download_url)
                else:
                    # 如果没有下载链接，使用默认链接
                    webbrowser.open("https://afdian.com/a/suaolin")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"无法打开浏览器: {str(e)}")

        # 无论选择什么都退出程序
        sys.exit(0)

    def check_access_code_startup(self):
        """启动时检查访问代码"""
        # 检查配置文件中是否有访问代码
        if os.path.exists(ENV_FILE):
            load_dotenv(ENV_FILE)
            saved_code = os.getenv("AUG_CODE", "").strip()

            if saved_code:
                # 有保存的代码，后台验证
                self.log_message("🔍 正在验证保存的访问代码...")
                self.background_verify_code(saved_code)
                return

        # 没有保存的代码，显示输入对话框
        self.show_access_code_dialog()

    def background_verify_code(self, code):
        """后台验证访问代码"""
        self.background_worker = WorkerThread("code_verify", code, parent=self)
        self.background_worker.finished.connect(lambda success, _: self.on_background_verify_finished(success, code))
        self.background_worker.start()

    def on_background_verify_finished(self, success, code):
        """后台验证完成回调"""
        if success:
            # 验证成功，设置访问代码并显示主界面
            self.access_code = code
            self.showMaximized()
            self.log_message("✅ 访问代码验证成功，欢迎使用 Augment New 客户端！")

            # 获取并显示账号余量
            self.update_account_usage()
        else:
            # 验证失败，显示输入对话框
            self.log_message("❌ 保存的访问代码已失效，请重新输入")
            self.show_access_code_dialog()

    def show_access_code_dialog(self):
        """显示访问代码验证对话框"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox

        dialog = QDialog(self)
        dialog.setWindowTitle("访问代码验证")
        dialog.setModal(True)
        dialog.setFixedSize(400, 200)
        dialog.setStyleSheet(self.get_modern_stylesheet())

        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("🔑 请输入访问代码")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border: 2px solid #667eea;
            }
        """)
        layout.addWidget(title_label)

        # 说明文字
        info_label = QLabel("如果您没有访问代码，请在 https://afdian.com/a/suaolin 购买")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 12px;
                background: transparent;
                border: none;
                padding: 0px;
            }
        """)
        layout.addWidget(info_label)

        # 输入框
        self.dialog_code_input = QLineEdit()
        self.dialog_code_input.setPlaceholderText("请输入您的访问代码")
        self.dialog_code_input.setEchoMode(QLineEdit.Password)
        self.dialog_code_input.setMinimumHeight(40)

        # 如果有保存的代码，自动填入
        if hasattr(self, 'access_code') and self.access_code:
            self.dialog_code_input.setText(self.access_code)

        layout.addWidget(self.dialog_code_input)

        # 按钮
        button_layout = QHBoxLayout()

        verify_btn = QPushButton("验证")
        verify_btn.setMinimumHeight(40)
        verify_btn.clicked.connect(lambda: self.verify_dialog_code(dialog))

        exit_btn = QPushButton("退出")
        exit_btn.setMinimumHeight(40)
        exit_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d62c1a, stop:1 #a93226);
            }
        """)
        exit_btn.clicked.connect(lambda: sys.exit(0))

        button_layout.addWidget(verify_btn)
        button_layout.addWidget(exit_btn)
        layout.addLayout(button_layout)

        # 回车键验证
        self.dialog_code_input.returnPressed.connect(lambda: self.verify_dialog_code(dialog))

        dialog.exec_()

    def verify_dialog_code(self, dialog):
        """验证对话框中的访问代码"""
        code = self.dialog_code_input.text().strip()
        if not code:
            QMessageBox.warning(dialog, "警告", "请输入访问代码")
            return

        # 禁用按钮，显示验证中
        for btn in dialog.findChildren(QPushButton):
            if btn.text() == "验证":
                btn.setText("验证中...")
                btn.setEnabled(False)

        # 启动验证
        self.dialog_worker = WorkerThread("code_verify", code, parent=self)
        self.dialog_worker.finished.connect(lambda success, _: self.on_dialog_verify_finished(success, code, dialog))
        self.dialog_worker.start()

    def on_dialog_verify_finished(self, success, code, dialog):
        """对话框验证完成回调"""
        if success:
            # 验证成功，保存代码并显示主界面
            self.access_code = code

            # 保存到配置文件
            self.save_access_code(code)

            dialog.accept()
            self.showMaximized()
            self.log_message("✅ 访问代码验证成功，欢迎使用 Augment New 客户端！")

            # 获取并显示账号余量
            self.update_account_usage()
        else:
            # 验证失败，重新启用按钮
            for btn in dialog.findChildren(QPushButton):
                if "验证" in btn.text():
                    btn.setText("验证")
                    btn.setEnabled(True)
            QMessageBox.critical(dialog, "验证失败", "访问代码无效，请检查后重试")

    def save_access_code(self, code):
        """保存访问代码到配置文件"""
        env_vars = {}
        if os.path.exists(ENV_FILE):
            with open(ENV_FILE, "r", encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and "=" in line:
                        key, value = line.split("=", 1)
                        env_vars[key] = value

        env_vars["AUG_CODE"] = code

        # 添加默认配置
        if "CURSOR" not in env_vars:
            env_vars["CURSOR"] = "false"
        if "CURSORMENTION" not in env_vars:
            env_vars["CURSORMENTION"] = "false"
        if "TERMS" not in env_vars:
            env_vars["TERMS"] = "true"

        with open(ENV_FILE, "w", encoding='utf-8') as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")

    def update_account_usage(self):
        """更新账号使用情况"""
        if not self.access_code:
            return

        self.usage_worker = WorkerThread("get_usage_info", self.access_code, parent=self)
        self.usage_worker.finished.connect(self.on_usage_update_finished)
        self.usage_worker.start()

    def on_usage_update_finished(self, success, result):
        """账号使用情况更新完成回调"""
        if success:
            try:
                # 解析使用信息
                import ast
                usage_info = ast.literal_eval(result)
                remaining = usage_info.get("remaining", 0)
                usage = usage_info.get("usage", 0)
                max_usage = usage_info.get("max_usage", 0)

                # 根据剩余次数设置颜色
                if remaining > 5:
                    color = "#28a745"  # 绿色
                    bg_color = "#d4edda"
                elif remaining > 0:
                    color = "#ffc107"  # 黄色
                    bg_color = "#fff3cd"
                else:
                    color = "#dc3545"  # 红色
                    bg_color = "#f8d7da"

                self.usage_label.setText(f"📊 账号余量: {remaining} 次 ({usage}/{max_usage})")
                self.usage_label.setStyleSheet(f"""
                    QLabel {{
                        color: {color};
                        font-size: 14px;
                        font-weight: bold;
                        padding: 8px 12px;
                        background-color: {bg_color};
                        border-radius: 4px;
                        border-left: 3px solid {color};
                    }}
                """)
            except Exception:
                self.usage_label.setText("📊 账号状态: 正常")
                self.usage_label.setStyleSheet("""
                    QLabel {
                        color: #28a745;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 8px 12px;
                        background-color: #d4edda;
                        border-radius: 4px;
                        border-left: 3px solid #28a745;
                    }
                """)
        else:
            self.usage_label.setText("📊 账号状态: 异常")
            self.usage_label.setStyleSheet("""
                QLabel {
                    color: #dc3545;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px 12px;
                    background-color: #f8d7da;
                    border-radius: 4px;
                    border-left: 3px solid #dc3545;
                }
            """)

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"Augment New 客户端 v{CURRENT_VERSION}")
        self.setGeometry(100, 100, 1200, 800)

        # 设置窗口图标
        if os.path.exists("favicon.ico"):
            self.setWindowIcon(QIcon("favicon.ico"))

        # 设置现代化样式
        self.setStyleSheet(self.get_modern_stylesheet())

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局（三栏分割）
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        splitter = QSplitter(Qt.Horizontal)
        splitter.setHandleWidth(2)
        main_layout.addWidget(splitter)

        # 左侧控制面板
        self.create_control_panel(splitter)

        # 中间日志面板
        self.create_log_panel(splitter)

        # 右侧账号管理面板
        self.create_account_panel(splitter)

        # 设置分割比例（左30%，中40%，右30%）
        splitter.setSizes([350, 450, 350])

    def get_modern_stylesheet(self):
        """获取现代化样式表"""
        return """
        /* 主窗口样式 */
        QMainWindow {
            background-color: #f8f9fa;
        }

        /* 分组框样式 */
        QGroupBox {
            font-weight: 600;
            font-size: 13px;
            color: #2c3e50;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin-top: 10px;
            padding-top: 10px;
            background-color: white;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            background-color: white;
        }

        /* 按钮样式 */
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            border: none;
            border-radius: 6px;
            color: white;
            font-weight: 600;
            font-size: 18px;
            padding: 16px 30px;
            min-height: 26px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #5a6fd8, stop:1 #6a4190);
        }

        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4e5bc6, stop:1 #5e377e);
        }

        QPushButton:disabled {
            background: #bdc3c7;
            color: #7f8c8d;
        }

        /* 输入框样式 */
        QLineEdit {
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 14px 18px;
            font-size: 18px;
            background-color: white;
            selection-background-color: #667eea;
        }

        QLineEdit:focus {
            border-color: #667eea;
            outline: none;
        }

        QLineEdit:read-only {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        /* 复选框样式 */
        QCheckBox {
            font-size: 18px;
            color: #495057;
            spacing: 14px;
        }

        QCheckBox::indicator {
            width: 22px;
            height: 22px;
            border-radius: 4px;
            border: 2px solid #dee2e6;
            background-color: white;
        }

        QCheckBox::indicator:checked {
            background-color: #667eea;
            border-color: #667eea;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }

        QCheckBox::indicator:hover {
            border-color: #667eea;
        }

        /* 文本编辑器样式 */
        QTextEdit {
            border: 2px solid #e9ecef;
            border-radius: 6px;
            background-color: #ffffff;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 15px;
            padding: 16px;
            line-height: 1.7;
        }

        /* 进度条样式 */
        QProgressBar {
            border: none;
            border-radius: 4px;
            background-color: #e9ecef;
            height: 6px;
        }

        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 4px;
        }

        /* 框架样式 */
        QFrame {
            background-color: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }

        /* 标签样式 */
        QLabel {
            color: #495057;
            font-size: 16px;
        }

        /* 分割器样式 */
        QSplitter::handle {
            background-color: #dee2e6;
        }

        QSplitter::handle:horizontal {
            width: 2px;
        }

        QSplitter::handle:vertical {
            height: 2px;
        }
        """

    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = QFrame()
        control_layout = QVBoxLayout(control_frame)
        control_layout.setContentsMargins(15, 15, 15, 15)
        control_layout.setSpacing(15)

        # 标题
        title_label = QLabel("🚀 Augment New 客户端")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                margin-bottom: 10px;
                border: 2px solid #667eea;
            }
        """)
        control_layout.addWidget(title_label)

        # 访问代码状态组
        code_group = QGroupBox("🔑 访问代码")
        code_layout = QVBoxLayout(code_group)
        code_layout.setContentsMargins(15, 20, 15, 15)
        code_layout.setSpacing(12)

        # 状态标签
        self.code_status_label = QLabel("✅ 访问代码已验证")
        self.code_status_label.setStyleSheet("""
            QLabel {
                color: #28a745;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background-color: #d4edda;
                border-radius: 4px;
                border-left: 3px solid #28a745;
            }
        """)
        code_layout.addWidget(self.code_status_label)

        # 账号余量显示
        self.usage_label = QLabel("📊 账号余量: 检查中...")
        self.usage_label.setStyleSheet("""
            QLabel {
                color: #667eea;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 12px;
                background-color: #e7f3ff;
                border-radius: 4px;
                border-left: 3px solid #667eea;
            }
        """)
        code_layout.addWidget(self.usage_label)

        control_layout.addWidget(code_group)

        # 应用选择配置组
        app_group = QGroupBox("🧹 缓存清理配置")
        app_layout = QVBoxLayout(app_group)
        app_layout.setContentsMargins(15, 20, 15, 15)
        app_layout.setSpacing(10)

        # 选择应用按钮
        self.select_apps_btn = QPushButton("📱 点击选择同时清理其他应用缓存")
        self.select_apps_btn.clicked.connect(self.show_app_selection_dialog)
        self.select_apps_btn.setMinimumHeight(40)
        self.select_apps_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                font-size: 14px;
                font-weight: bold;
                text-align: left;
                padding-left: 15px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #117a8b);
            }
        """)
        app_layout.addWidget(self.select_apps_btn)

        # 当前选择显示
        self.selected_apps_label = QLabel("当前选择: VSCode")
        self.selected_apps_label.setWordWrap(True)
        self.selected_apps_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                padding: 12px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border-left: 3px solid #17a2b8;
            }
        """)
        app_layout.addWidget(self.selected_apps_label)

        app_info = QLabel("💡 默认清理 VSCode 缓存，可选择同时清理 Cursor、IntelliJ IDEA、PyCharm 等")
        app_info.setWordWrap(True)
        app_info.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 13px;
                padding: 10px;
                background-color: #e7f3ff;
                border-radius: 4px;
                border-left: 3px solid #667eea;
            }
        """)
        app_layout.addWidget(app_info)

        control_layout.addWidget(app_group)

        # 工作流步骤指示器
        workflow_group = QGroupBox("📋 工作流进度")
        workflow_layout = QVBoxLayout(workflow_group)
        workflow_layout.setContentsMargins(15, 20, 15, 15)
        workflow_layout.setSpacing(8)

        # 创建步骤指示器
        steps = [
            "🧹 清理缓存",
            "📧 获取临时邮箱",
            "🔄 获取验证码"
        ]

        self.workflow_step_labels = []
        for i, step in enumerate(steps, 1):
            step_label = QLabel(f"步骤 {i}: {step}")
            step_label.setStyleSheet("""
                QLabel {
                    color: #6c757d;
                    font-size: 14px;
                    padding: 8px 12px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                    border-left: 3px solid #dee2e6;
                }
            """)
            self.workflow_step_labels.append(step_label)
            workflow_layout.addWidget(step_label)

        control_layout.addWidget(workflow_group)

        # 操作按钮组
        action_group = QGroupBox("🛠️ 操作")
        action_layout = QVBoxLayout(action_group)
        action_layout.setContentsMargins(15, 20, 15, 15)
        action_layout.setSpacing(10)

        # 一键式工作流按钮
        self.workflow_btn = QPushButton("🚀 一键式工作流")
        self.workflow_btn.clicked.connect(self.start_one_click_workflow)
        self.workflow_btn.setMinimumHeight(50)
        self.workflow_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20c997);
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #218838, stop:1 #1e7e34);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1e7e34, stop:1 #155724);
            }
        """)
        action_layout.addWidget(self.workflow_btn)

        # 验证码监听状态显示
        self.verification_status_label = QLabel("⏳ 等待开始监听验证码...")
        self.verification_status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                padding: 8px 12px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border-left: 3px solid #6c757d;
            }
        """)
        action_layout.addWidget(self.verification_status_label)

        control_layout.addWidget(action_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)

        # 弹性空间
        control_layout.addStretch()

        parent.addWidget(control_frame)

    def create_log_panel(self, parent):
        """创建右侧日志面板"""
        log_frame = QFrame()
        log_layout = QVBoxLayout(log_frame)
        log_layout.setContentsMargins(15, 15, 15, 15)
        log_layout.setSpacing(15)

        # 日志标题
        log_title = QLabel("📋 操作日志")
        log_title.setFont(QFont("Arial", 14, QFont.Bold))
        log_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #667eea;
            }
        """)
        log_layout.addWidget(log_title)

        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 16))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #ffffff;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                padding: 12px;
                line-height: 1.6;
                font-size: 16px;
            }
        """)
        log_layout.addWidget(self.log_text)

        # 清空日志按钮
        clear_btn = QPushButton("🗑️ 清空日志")
        clear_btn.clicked.connect(self.clear_log)
        clear_btn.setMinimumHeight(40)
        clear_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d62c1a, stop:1 #a93226);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #c0392b, stop:1 #922b21);
            }
        """)
        log_layout.addWidget(clear_btn)

        parent.addWidget(log_frame)

    def create_account_panel(self, parent):
        """创建右侧账号管理面板"""
        account_frame = QFrame()
        account_layout = QVBoxLayout(account_frame)
        account_layout.setContentsMargins(15, 15, 15, 15)
        account_layout.setSpacing(15)

        # 账号管理标题
        account_title = QLabel("👥 已注册账号")
        account_title.setFont(QFont("Arial", 14, QFont.Bold))
        account_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border-left: 4px solid #28a745;
            }
        """)
        account_layout.addWidget(account_title)

        # 账号列表
        self.account_list = QListWidget()
        self.account_list.setMinimumHeight(200)
        self.account_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #e9ecef;
                border-radius: 6px;
                background-color: white;
                font-size: 14px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f1f3f4;
                border-radius: 4px;
                margin: 2px;
                line-height: 1.4;
            }
            QListWidget::item:hover {
                background-color: #f8f9fa;
            }
            QListWidget::item:selected {
                background-color: #e7f3ff;
                color: #2c3e50;
                border-left: 3px solid #667eea;
            }
        """)
        account_layout.addWidget(self.account_list)

        # 账号操作按钮
        account_btn_layout = QHBoxLayout()

        # 获取验证码按钮
        self.get_code_btn = QPushButton("🔄 获取验证码")
        self.get_code_btn.clicked.connect(self.get_verification_for_selected_account)
        self.get_code_btn.setEnabled(False)
        self.get_code_btn.setMinimumHeight(35)
        self.get_code_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #117a8b);
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)

        # 删除账号按钮
        self.delete_account_btn = QPushButton("🗑️ 删除")
        self.delete_account_btn.clicked.connect(self.delete_selected_account)
        self.delete_account_btn.setEnabled(False)
        self.delete_account_btn.setMinimumHeight(35)
        self.delete_account_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e74c3c, stop:1 #c0392b);
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d62c1a, stop:1 #a93226);
            }
            QPushButton:disabled {
                background: #bdc3c7;
                color: #7f8c8d;
            }
        """)

        account_btn_layout.addWidget(self.get_code_btn)
        account_btn_layout.addWidget(self.delete_account_btn)
        account_layout.addLayout(account_btn_layout)

        # 账号信息显示
        self.account_info_label = QLabel("💡 选择账号后可重新获取验证码")
        self.account_info_label.setWordWrap(True)
        self.account_info_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                padding: 10px;
                background-color: #e7f3ff;
                border-radius: 4px;
                border-left: 3px solid #667eea;
            }
        """)
        account_layout.addWidget(self.account_info_label)

        # 监听状态显示
        self.listening_status_label = QLabel("🔇 未在监听")
        self.listening_status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                padding: 8px 12px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border-left: 3px solid #6c757d;
                text-align: center;
            }
        """)
        account_layout.addWidget(self.listening_status_label)

        # 弹性空间
        account_layout.addStretch()

        # 连接账号列表选择事件
        self.account_list.itemSelectionChanged.connect(self.on_account_selection_changed)

        parent.addWidget(account_frame)

    def show_app_selection_dialog(self):
        """显示应用选择对话框"""
        dialog = AppSelectionDialog(self, self.selected_apps)
        if dialog.exec_() == QDialog.Accepted:
            self.selected_apps = dialog.get_selected_apps()
            if not self.selected_apps:  # 如果没有选择任何应用，默认选择 VSCode
                self.selected_apps = ['vscode']
            self.update_selected_apps_display()
            self.save_app_config()
            self.log_message(f"✅ 已更新缓存清理配置: {', '.join([app.upper() for app in self.selected_apps])}")

    def update_selected_apps_display(self):
        """更新选中应用的显示"""
        app_names = {
            'vscode': 'VSCode',
            'cursor': 'Cursor',
            'idea': 'IntelliJ IDEA',
            'pycharm': 'PyCharm'
        }

        display_names = [app_names.get(app, app.upper()) for app in self.selected_apps]
        self.selected_apps_label.setText(f"当前选择: {', '.join(display_names)}")

    def on_account_selection_changed(self):
        """账号选择变化时的处理"""
        selected_items = self.account_list.selectedItems()
        has_selection = len(selected_items) > 0

        self.get_code_btn.setEnabled(has_selection and bool(self.access_code))
        self.delete_account_btn.setEnabled(has_selection)

        if has_selection:
            email = selected_items[0].text().split('\n')[0]  # 获取邮箱地址
            self.account_info_label.setText(f"📧 选中账号: {email}")
        else:
            self.account_info_label.setText("💡 选择账号后可重新获取验证码")

    def add_registered_account(self, email):
        """添加已注册账号到列表"""
        # 检查是否已存在
        for i in range(self.account_list.count()):
            item = self.account_list.item(i)
            if email in item.text():
                return  # 已存在，不重复添加

        # 添加新账号
        import datetime
        timestamp = datetime.datetime.now().strftime("%m-%d %H:%M")
        item_text = f"{email}\n验证码获取: {timestamp}"

        item = QListWidgetItem(item_text)
        item.setData(Qt.UserRole, email)
        self.account_list.addItem(item)

        # 保存到配置
        self.registered_accounts.append({
            'email': email,
            'timestamp': timestamp
        })
        self.save_registered_accounts()

        self.log_message(f"✅ 已添加成功获取验证码的账号到管理列表: {email}")

    def delete_selected_account(self):
        """删除选中的账号"""
        selected_items = self.account_list.selectedItems()
        if not selected_items:
            return

        item = selected_items[0]
        email = item.data(Qt.UserRole)

        # 确认删除
        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除账号 {email} 吗？",
                                   QMessageBox.Yes | QMessageBox.No,
                                   QMessageBox.No)

        if reply == QMessageBox.Yes:
            # 从列表中移除
            row = self.account_list.row(item)
            self.account_list.takeItem(row)

            # 从配置中移除
            self.registered_accounts = [acc for acc in self.registered_accounts if acc['email'] != email]
            self.save_registered_accounts()

            self.log_message(f"🗑️ 已删除账号: {email}")

    def get_verification_for_selected_account(self):
        """为选中账号获取验证码"""
        selected_items = self.account_list.selectedItems()
        if not selected_items:
            return

        email = selected_items[0].data(Qt.UserRole)
        self.start_verification_listening(email)

    def load_config(self):
        """加载配置文件"""
        if os.path.exists(ENV_FILE):
            load_dotenv(ENV_FILE)

            # 加载访问代码
            code = os.getenv("AUG_CODE", "")
            if code:
                self.access_code = code

            # 加载应用选择配置
            selected_apps_str = os.getenv("SELECTED_APPS", "vscode")
            self.selected_apps = [app.strip() for app in selected_apps_str.split(",") if app.strip()]
            if not self.selected_apps:
                self.selected_apps = ['vscode']

            self.update_selected_apps_display()

        # 加载已注册账号
        self.load_registered_accounts()

        self.log_message("✅ 配置加载完成")

    def save_config(self):
        """保存配置"""
        # 保存应用选择配置
        self.save_app_config()
        self.log_message("💾 配置已保存")

    def save_app_config(self):
        """保存应用选择配置到环境文件"""
        env_vars = {}
        if os.path.exists(ENV_FILE):
            with open(ENV_FILE, "r", encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and "=" in line:
                        key, value = line.split("=", 1)
                        env_vars[key] = value

        # 更新应用选择配置
        env_vars["SELECTED_APPS"] = ",".join(self.selected_apps)

        # 保持向后兼容性，设置 CURSOR 变量
        env_vars["CURSOR"] = "true" if "cursor" in self.selected_apps else "false"
        env_vars["CURSORMENTION"] = "true"

        with open(ENV_FILE, "w", encoding='utf-8') as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")

    def load_registered_accounts(self):
        """加载已注册账号"""
        accounts_file = "registered_accounts.json"
        if os.path.exists(accounts_file):
            try:
                import json
                with open(accounts_file, "r", encoding='utf-8') as f:
                    self.registered_accounts = json.load(f)

                # 更新界面显示
                self.account_list.clear()
                for account in self.registered_accounts:
                    email = account['email']
                    timestamp = account['timestamp']
                    item_text = f"{email}\n验证码获取: {timestamp}"

                    item = QListWidgetItem(item_text)
                    item.setData(Qt.UserRole, email)
                    self.account_list.addItem(item)

                if self.registered_accounts:
                    self.log_message(f"📋 已加载 {len(self.registered_accounts)} 个已注册账号")
            except Exception as e:
                self.log_message(f"❌ 加载账号配置失败: {str(e)}")
                self.registered_accounts = []

    def save_registered_accounts(self):
        """保存已注册账号"""
        accounts_file = "registered_accounts.json"
        try:
            import json
            with open(accounts_file, "w", encoding='utf-8') as f:
                json.dump(self.registered_accounts, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"❌ 保存账号配置失败: {str(e)}")

    def log_message(self, message):
        """添加日志消息"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")

    def get_timestamp(self):
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()

    def show_progress(self, show=True):
        """显示/隐藏进度条"""
        self.progress_bar.setVisible(show)
        if show:
            self.progress_bar.setRange(0, 0)  # 无限进度条

    def update_workflow_step(self, step_number, status="current"):
        """更新工作流步骤状态"""
        if 1 <= step_number <= len(self.workflow_step_labels):
            label = self.workflow_step_labels[step_number - 1]

            if status == "current":
                # 当前步骤 - 蓝色
                label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 8px 12px;
                        background-color: #667eea;
                        border-radius: 4px;
                        border-left: 3px solid #4e5bc6;
                    }
                """)
            elif status == "completed":
                # 已完成步骤 - 绿色
                label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 8px 12px;
                        background-color: #28a745;
                        border-radius: 4px;
                        border-left: 3px solid #20c997;
                    }
                """)
            elif status == "failed":
                # 失败步骤 - 红色
                label.setStyleSheet("""
                    QLabel {
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 8px 12px;
                        background-color: #dc3545;
                        border-radius: 4px;
                        border-left: 3px solid #c82333;
                    }
                """)
            else:
                # 默认状态 - 灰色
                label.setStyleSheet("""
                    QLabel {
                        color: #6c757d;
                        font-size: 14px;
                        padding: 8px 12px;
                        background-color: #f8f9fa;
                        border-radius: 4px;
                        border-left: 3px solid #dee2e6;
                    }
                """)

    def reset_workflow_steps(self):
        """重置所有工作流步骤状态"""
        for i in range(len(self.workflow_step_labels)):
            self.update_workflow_step(i + 1, "default")
        self.current_step = 0

    def start_one_click_workflow(self):
        """启动一键式工作流"""
        # 检查访问代码
        if not self.access_code:
            QMessageBox.warning(self, "警告", "请先验证访问代码")
            return

        # 重置工作流状态
        self.reset_workflow_steps()
        self.log_message("🚀 开始一键式工作流...")

        # 保存配置
        self.save_config()

        # 禁用所有控制按钮
        self.set_controls_enabled(False)

        # 显示进度条
        self.show_progress(True)

        # 启动工作流
        self.worker = WorkerThread("one_click_workflow", parent=self)
        self.worker.workflow_step.connect(self.on_workflow_step)
        self.worker.step_completed.connect(self.on_step_completed)
        self.worker.finished.connect(self.on_workflow_finished)
        self.worker.start()

    def set_controls_enabled(self, enabled):
        """设置控制按钮的启用状态"""
        self.workflow_btn.setEnabled(enabled)
        self.select_apps_btn.setEnabled(enabled)

        # 账号管理按钮状态
        selected_items = self.account_list.selectedItems()
        has_selection = len(selected_items) > 0
        self.get_code_btn.setEnabled(enabled and has_selection and bool(self.access_code))

    def on_workflow_step(self, step_number, description):
        """工作流步骤更新回调"""
        self.current_step = step_number
        self.update_workflow_step(step_number, "current")
        self.log_message(f"📋 步骤 {step_number}: {description}")

    def on_step_completed(self, step_number, result):
        """步骤完成回调"""
        if "失败" in result:
            self.update_workflow_step(step_number, "failed")
            self.log_message(f"❌ 步骤 {step_number}: {result}")
        else:
            self.update_workflow_step(step_number, "completed")
            self.log_message(f"✅ 步骤 {step_number}: {result}")

    def on_workflow_finished(self, success, result):
        """工作流完成回调"""
        self.show_progress(False)

        if success and result.startswith("workflow_completed:"):
            # 工作流成功完成
            email = result.split(":", 1)[1]
            self.current_email = email

            # 重新启用控制按钮
            self.set_controls_enabled(True)
            self.log_message("🎉 一键式工作流完成！")

            # 显示确认对话框
            if self.show_email_confirmation_dialog(email):
                # 复制邮箱到剪贴板
                try:
                    from PyQt5.QtWidgets import QApplication
                    clipboard = QApplication.clipboard()
                    clipboard.setText(email)
                    self.log_message(f"📋 邮箱已复制到剪贴板: {email}")
                except Exception as e:
                    self.log_message(f"❌ 复制到剪贴板失败: {str(e)}")

                # 打开注册页面
                try:
                    webbrowser.open("https://app.augmentcode.com/")
                    self.log_message("🌐 已打开 Augment Code 注册页面")

                    # 开始自动监听验证码
                    self.start_verification_listening(email)

                except Exception as e:
                    self.log_message(f"❌ 打开浏览器失败: {str(e)}")
                    QMessageBox.warning(self, "错误", f"无法打开浏览器: {str(e)}")

        else:
            # 工作流失败，重新启用控制按钮
            self.set_controls_enabled(True)
            self.log_message(f"❌ 工作流失败: {result}")
            QMessageBox.critical(self, "错误", f"一键式工作流失败:\n{result}")

    def show_email_confirmation_dialog(self, email):
        """显示邮箱确认对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("工作流完成")
        dialog.setModal(True)
        dialog.setFixedSize(500, 350)
        dialog.setStyleSheet(self.get_modern_stylesheet())

        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # 标题
        title_label = QLabel("🎉 一键式工作流已完成！")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #28a745;
                padding: 15px;
                background-color: #d4edda;
                border-radius: 8px;
                border: 2px solid #28a745;
            }
        """)
        layout.addWidget(title_label)

        # 邮箱显示说明
        email_label = QLabel("临时邮箱地址已生成：")
        email_label.setAlignment(Qt.AlignCenter)
        email_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 14px;
                background: transparent;
                border: none;
                padding: 0px;
            }
        """)
        layout.addWidget(email_label)

        # 邮箱地址显示框
        email_input = QLineEdit(email)
        email_input.setReadOnly(True)
        email_input.setAlignment(Qt.AlignCenter)
        email_input.setFont(QFont("Arial", 14, QFont.Bold))
        email_input.setMinimumHeight(45)
        email_input.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 2px solid #17a2b8;
                border-radius: 8px;
                color: #17a2b8;
                font-size: 16px;
                font-weight: bold;
                text-align: center;
                padding: 10px;
            }
        """)
        layout.addWidget(email_input)

        # 说明文字
        info_label = QLabel("💡 点击'确认'将自动复制邮箱到剪贴板并打开注册页面")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 13px;
                background: transparent;
                border: none;
                padding: 0px;
            }
        """)
        layout.addWidget(info_label)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        confirm_btn = QPushButton("确认")
        confirm_btn.setMinimumHeight(40)
        confirm_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #28a745, stop:1 #20c997);
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #218838, stop:1 #1e7e34);
            }
        """)
        confirm_btn.clicked.connect(dialog.accept)

        cancel_btn = QPushButton("取消")
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(dialog.reject)

        button_layout.addWidget(confirm_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # 自动选中邮箱文本
        email_input.selectAll()
        email_input.setFocus()

        return dialog.exec_() == QDialog.Accepted

    def start_verification_listening(self, email):
        """开始监听验证码"""
        if not self.access_code:
            QMessageBox.warning(self, "警告", "请先验证访问代码")
            return

        if not email:
            QMessageBox.warning(self, "警告", "邮箱地址不能为空")
            return

        # 停止之前的监听
        self.stop_verification_listening()

        # 记录当前监听的邮箱
        self.current_listening_email = email

        self.log_message(f"🔄 开始为 {email} 监听验证码（最长300秒）...")

        # 更新状态显示
        self.verification_status_label.setText("🔄 正在监听验证码...")
        self.verification_status_label.setStyleSheet("""
            QLabel {
                color: #17a2b8;
                font-size: 14px;
                padding: 8px 12px;
                background-color: #d1ecf1;
                border-radius: 4px;
                border-left: 3px solid #17a2b8;
            }
        """)

        self.listening_status_label.setText("🔊 正在监听验证码...")
        self.listening_status_label.setStyleSheet("""
            QLabel {
                color: #17a2b8;
                font-size: 13px;
                padding: 8px 12px;
                background-color: #d1ecf1;
                border-radius: 4px;
                border-left: 3px solid #17a2b8;
                text-align: center;
            }
        """)

        # 禁用相关按钮
        self.set_controls_enabled(False)
        self.get_code_btn.setEnabled(False)

        # 启动监听工作线程
        self.verification_worker = VerificationListenerThread(self.access_code, email, 300)
        self.verification_worker.verification_received.connect(self.on_verification_received)
        self.verification_worker.status_update.connect(self.on_verification_status_update)
        self.verification_worker.finished.connect(self.on_verification_listening_finished)
        self.verification_worker.start()

    def stop_verification_listening(self):
        """停止验证码监听"""
        if self.verification_worker and self.verification_worker.isRunning():
            self.verification_worker.stop_listening()
            self.verification_worker.wait(3000)  # 等待最多3秒
            if self.verification_worker.isRunning():
                self.verification_worker.terminate()

            # 手动触发完成信号（如果线程没有自己发送的话）
            if hasattr(self, 'current_listening_email') and self.current_listening_email:
                # 如果是因为获取到验证码而停止，标记为成功
                self.on_verification_listening_finished(True, "验证码获取成功")

        # 清空当前监听的邮箱
        self.current_listening_email = ""

    def on_verification_received(self, verification_code):
        """收到验证码时的处理"""
        self.log_message(f"✅ 验证码获取成功: {verification_code}")

        # 成功获取验证码后，将邮箱添加到已注册账号列表
        if self.current_listening_email:
            self.add_registered_account(self.current_listening_email)

        # 停止监听（这会触发 on_verification_listening_finished）
        self.stop_verification_listening()

        # 显示验证码对话框
        self.show_verification_code_dialog(verification_code)

    def on_verification_status_update(self, remaining_time):
        """验证码监听状态更新"""
        minutes = remaining_time // 60
        seconds = remaining_time % 60
        self.listening_status_label.setText(f"🔊 监听中... 剩余 {minutes:02d}:{seconds:02d}")

    def on_verification_listening_finished(self, success, message):
        """验证码监听完成"""
        # 重新启用控制按钮
        self.set_controls_enabled(True)

        # 更新状态显示
        if success:
            self.verification_status_label.setText("✅ 验证码监听完成")
            self.verification_status_label.setStyleSheet("""
                QLabel {
                    color: #28a745;
                    font-size: 14px;
                    padding: 8px 12px;
                    background-color: #d4edda;
                    border-radius: 4px;
                    border-left: 3px solid #28a745;
                }
            """)
            # 成功获取验证码后，重置到初始状态
            self.reset_to_initial_state()
        else:
            self.verification_status_label.setText("⏰ 验证码监听超时")
            self.verification_status_label.setStyleSheet("""
                QLabel {
                    color: #dc3545;
                    font-size: 14px;
                    padding: 8px 12px;
                    background-color: #f8d7da;
                    border-radius: 4px;
                    border-left: 3px solid #dc3545;
                }
            """)
            self.log_message("⏰ 验证码监听超时，请检查邮箱注册状态或稍后重试")
            # 超时后也重置到初始状态
            self.reset_to_initial_state()

        self.listening_status_label.setText("🔇 未在监听")
        self.listening_status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 13px;
                padding: 8px 12px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border-left: 3px solid #6c757d;
                text-align: center;
            }
        """)

    def reset_to_initial_state(self):
        """重置程序到初始状态"""
        # 延迟3秒后重置，让用户有时间看到完成状态
        QTimer.singleShot(3000, self.perform_reset)

    def perform_reset(self):
        """执行重置操作"""
        # 清空当前邮箱
        self.current_email = ""
        self.current_listening_email = ""

        # 重置工作流步骤指示器
        self.current_step = 0
        for i, label in enumerate(self.workflow_step_labels):
            label.setStyleSheet("""
                QLabel {
                    color: #6c757d;
                    font-size: 14px;
                    padding: 8px 12px;
                    background-color: #f8f9fa;
                    border-radius: 4px;
                    border-left: 3px solid #dee2e6;
                }
            """)

        # 重置验证码监听状态
        self.verification_status_label.setText("⏳ 等待开始监听验证码...")
        self.verification_status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                padding: 8px 12px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border-left: 3px solid #6c757d;
            }
        """)

        self.listening_status_label.setText("🔇 未在监听")
        self.listening_status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 13px;
                padding: 8px 12px;
                background-color: #f8f9fa;
                border-radius: 4px;
                border-left: 3px solid #6c757d;
                text-align: center;
            }
        """)

        # 重新启用控制按钮
        self.set_controls_enabled(True)

        # 隐藏进度条
        self.show_progress(False)

        self.log_message("🔄 程序已重置到初始状态，可以开始新的工作流")

    def get_verification_code(self):
        """获取验证码"""
        if not self.access_code:
            QMessageBox.warning(self, "警告", "请先验证访问代码")
            return

        if not self.current_email:
            QMessageBox.warning(self, "警告", "请先完成一键式工作流获取临时邮箱")
            return

        self.log_message("🔄 正在获取验证码，请稍候...")

        # 禁用控制按钮
        self.set_controls_enabled(False)
        self.show_progress(True)

        self.worker = WorkerThread("get_verification", self.access_code, self.current_email, parent=self)
        self.worker.finished.connect(self.on_get_verification_finished)
        self.worker.start()

    def on_get_verification_finished(self, success, result):
        """获取验证码完成回调"""
        self.show_progress(False)

        # 重新启用控制按钮
        self.set_controls_enabled(True)

        if success:
            self.log_message(f"✅ 验证码获取成功: {result}")
            self.show_verification_code_dialog(result)
        else:
            self.log_message("❌ 验证码获取失败")
            QMessageBox.critical(self, "错误", "验证码获取失败")

    def show_verification_code_dialog(self, verification_code):
        """显示验证码对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("验证码获取成功")
        dialog.setModal(True)
        dialog.setFixedSize(450, 300)
        dialog.setStyleSheet(self.get_modern_stylesheet())

        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # 标题
        title_label = QLabel("🎉 验证码获取成功！")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #28a745;
                padding: 15px;
                background-color: #d4edda;
                border-radius: 8px;
                border: 2px solid #28a745;
            }
        """)
        layout.addWidget(title_label)

        # 验证码显示
        code_label = QLabel(f"您的验证码是：")
        code_label.setAlignment(Qt.AlignCenter)
        code_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 14px;
                background: transparent;
                border: none;
                padding: 0px;
            }
        """)
        layout.addWidget(code_label)

        # 验证码文本框（只读，方便复制）
        code_input = QLineEdit(verification_code)
        code_input.setReadOnly(True)
        code_input.setAlignment(Qt.AlignCenter)
        code_input.setFont(QFont("Arial", 18, QFont.Bold))
        code_input.setMinimumHeight(50)
        code_input.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 2px solid #28a745;
                border-radius: 8px;
                color: #28a745;
                font-size: 20px;
                font-weight: bold;
                text-align: center;
                padding: 10px;
            }
        """)
        layout.addWidget(code_input)

        # 说明文字
        info_label = QLabel("💡 验证码已自动选中，您可以直接复制使用")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 13px;
                background: transparent;
                border: none;
                padding: 0px;
            }
        """)
        layout.addWidget(info_label)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        copy_btn = QPushButton("📋 复制验证码")
        copy_btn.setMinimumHeight(40)
        copy_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #17a2b8, stop:1 #138496);
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #138496, stop:1 #117a8b);
            }
        """)
        copy_btn.clicked.connect(lambda: self.copy_verification_code(verification_code, copy_btn))

        close_btn = QPushButton("关闭")
        close_btn.setMinimumHeight(40)
        close_btn.clicked.connect(dialog.accept)

        button_layout.addWidget(copy_btn)
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

        # 自动选中验证码文本
        code_input.selectAll()
        code_input.setFocus()

        dialog.exec_()

        # 验证码对话框关闭后，触发重置（如果还没有重置的话）
        # 这里不直接调用重置，因为 on_verification_listening_finished 会处理

    def copy_verification_code(self, code, button):
        """复制验证码到剪贴板"""
        try:
            clipboard = QApplication.clipboard()
            clipboard.setText(code)
            button.setText("✅ 已复制")
            QTimer.singleShot(2000, lambda: button.setText("📋 复制验证码"))
            self.log_message(f"📋 验证码已复制到剪贴板: {code}")
        except Exception as e:
            self.log_message(f"❌ 复制失败: {str(e)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyle('Fusion')

    # 创建主窗口
    window = AugmentClientGUI()
    window.showMaximized()  # 默认最大化显示

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
