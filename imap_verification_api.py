#!/usr/bin/env python3
"""
IMAP Verification API

A Flask API that verifies codes against usage.json and retrieves verification codes from emails.
Additional features include code validity checking and mail.txt content retrieval.
"""

from flask import Flask, request, jsonify
import json
import imaplib
import email
import re
import os

app = Flask(__name__)

# IMAP Configuration
IMAP_SERVER = "imap.qq.com"
USERNAME = "<EMAIL>"
PASSWORD = "xwiwpfxzpqymciae"

# Path to files
USAGE_FILE = "usage.json"
MAIL_FILE = "mail.txt"
VERSION_FILE = "version.txt"

def load_usage_data():
    """Load usage data from JSON file."""
    if not os.path.exists(USAGE_FILE):
        return []

    try:
        with open(USAGE_FILE, 'r') as file:
            return json.load(file)
    except json.JSONDecodeError:
        app.logger.error(f"Error decoding {USAGE_FILE}")
        return []
    except Exception as e:
        app.logger.error(f"Error loading {USAGE_FILE}: {str(e)}")
        return []

def save_usage_data(data):
    """Save usage data to JSON file."""
    try:
        with open(USAGE_FILE, 'w') as file:
            json.dump(data, file, indent=4)
        return True
    except Exception as e:
        app.logger.error(f"Error saving {USAGE_FILE}: {str(e)}")
        return False

def check_code(code):
    """
    Check if the code exists and has available usage.

    Returns:
        tuple: (is_valid, message, code_data)
    """
    usage_data = load_usage_data()

    for item in usage_data:
        if item.get("code") == code:
            if item.get("usage", 0) < item.get("max_usage", 0):
                return True, "Code is valid", item
            else:
                return False, "Code has reached maximum usage", item

    return False, "Invalid code", None

def update_usage(code):
    """Increment usage count for the given code."""
    usage_data = load_usage_data()
    updated = False

    for item in usage_data:
        if item.get("code") == code:
            item["usage"] = item.get("usage", 0) + 1
            updated = True
            break

    if updated:
        save_usage_data(usage_data)

    return updated

def get_verification_code_from_email(to_email):
    """
    Connect to IMAP server and get verification code from the latest email sent to to_email.

    Returns:
        tuple: (success, message, verification_code)
    """
    try:
        # Connect to IMAP server
        mail = imaplib.IMAP4_SSL(IMAP_SERVER)
        mail.login(USERNAME, PASSWORD)
        mail.select("inbox")

        # Search for emails sent to the specified recipient
        search_criteria = f'(TO "{to_email}")'
        status, data = mail.search(None, search_criteria)

        if status != "OK" or not data[0]:
            return False, "No emails found for the recipient", None

        # Get the latest email
        latest_email_id = data[0].split()[-1]
        status, data = mail.fetch(latest_email_id, "(RFC822)")

        if status != "OK":
            return False, "Failed to fetch email", None

        # Parse email content
        raw_email = data[0][1]
        msg = email.message_from_bytes(raw_email)

        # Extract email body
        body = ""
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))

                if "attachment" not in content_disposition and content_type in ["text/plain", "text/html"]:
                    try:
                        body = part.get_payload(decode=True).decode()
                        break
                    except:
                        pass
        else:
            body = msg.get_payload(decode=True).decode()

        # Search for verification code pattern
        pattern = r"Your verification code is: (\d+)"
        match = re.search(pattern, body)

        if match:
            verification_code = match.group(1)
            return True, "Verification code found", verification_code
        else:
            return False, "Verification code not found in email", None

    except imaplib.IMAP4.error as e:
        return False, f"IMAP error: {str(e)}", None
    except Exception as e:
        return False, f"Error: {str(e)}", None
    finally:
        try:
            mail.close()
            mail.logout()
        except:
            pass

@app.route('/')
def check_code_validity():
    """API endpoint to check code validity."""
    # Get code parameter
    code = request.args.get('code')

    # Validate parameter
    if not code:
        return jsonify({
            "status": False,
            "message": "Missing required parameter: code"
        }), 400

    # Check code validity
    is_valid, message, code_data = check_code(code)

    # Return response
    return jsonify({
        "status": is_valid,
        "message": message,
        "code_data": code_data
    })

@app.route('/mail')
def get_mail_content():
    """API endpoint to retrieve mail.txt content."""
    try:
        # Check if mail.txt exists
        if not os.path.exists(MAIL_FILE):
            return jsonify({
                "status": False,
                "message": f"File {MAIL_FILE} not found"
            }), 404

        # Read mail.txt content
        with open(MAIL_FILE, 'r') as file:
            content = file.read()

        # Return content
        return jsonify({
            "status": True,
            "content": content
        })
    except Exception as e:
        return jsonify({
            "status": False,
            "message": f"Error reading {MAIL_FILE}: {str(e)}"
        }), 500

@app.route('/version')
def get_version():
    """API endpoint to retrieve version information."""
    try:
        # Check if version.txt exists
        if not os.path.exists(VERSION_FILE):
            return jsonify({
                "status": False,
                "message": f"File {VERSION_FILE} not found"
            }), 404

        # Read version.txt content (first two lines)
        with open(VERSION_FILE, 'r') as file:
            lines = file.readlines()

        # Extract version and download URL
        version = lines[0].strip() if len(lines) > 0 else ""
        download_url = lines[1].strip() if len(lines) > 1 else ""

        # Return version information
        return jsonify({
            "status": True,
            "version": version,
            "download_url": download_url
        })
    except Exception as e:
        return jsonify({
            "status": False,
            "message": f"Error reading {VERSION_FILE}: {str(e)}"
        }), 500

@app.route('/verify')
def verify_code():
    """API endpoint to verify code and get verification code from email."""
    # Get parameters
    code = request.args.get('code')
    to_email = request.args.get('to')

    # Validate parameters
    if not code or not to_email:
        return jsonify({
            "status": False,
            "message": "Missing required parameters: code and to"
        }), 400

    # Check code validity
    is_valid, message, code_data = check_code(code)

    if not is_valid:
        return jsonify({
            "status": False,
            "message": message,
            "code_data": code_data
        }), 400

    # Get verification code from email
    success, email_message, verification_code = get_verification_code_from_email(to_email)

    if not success:
        return jsonify({
            "status": False,
            "message": email_message
        }), 400

    # Update usage count
    update_usage(code)

    # Return success response
    return jsonify({
        "status": True,
        "message": "Verification code retrieved successfully",
        "verification_code": verification_code,
        "code_data": {
            "code": code_data.get("code"),
            "usage": code_data.get("usage") + 1,  # Include the current usage
            "max_usage": code_data.get("max_usage")
        }
    })

@app.errorhandler(Exception)
def handle_error(e):
    """Global error handler."""
    app.logger.error(f"Unhandled exception: {str(e)}")
    return jsonify({
        "status": False,
        "message": f"Server error: {str(e)}"
    }), 500

if __name__ == "__main__":
    # Create usage.json if it doesn't exist
    if not os.path.exists(USAGE_FILE):
        sample_data = [
            {
                "code": "ABC123",
                "usage": 0,
                "max_usage": 10
            },
            {
                "code": "XYZ789",
                "usage": 0,
                "max_usage": 5
            },
            {
                "code": "LMN456",
                "usage": 0,
                "max_usage": 3
            }
        ]
        with open(USAGE_FILE, 'w') as file:
            json.dump(sample_data, file, indent=4)
        print(f"Created sample {USAGE_FILE}")

    # Create mail.txt if it doesn't exist
    if not os.path.exists(MAIL_FILE):
        with open(MAIL_FILE, 'w') as file:
            file.write("This is a sample mail content file.\nYou can modify this file to change the content returned by the /mail endpoint.")
        print(f"Created sample {MAIL_FILE}")

    # Create version.txt if it doesn't exist
    if not os.path.exists(VERSION_FILE):
        with open(VERSION_FILE, 'w') as file:
            file.write("1.0.0\nhttps://example.com/download/app-v1.0.0.zip")
        print(f"Created sample {VERSION_FILE}")

    # Run the Flask app
    app.run(host='0.0.0.0', port=5000, debug=True)
