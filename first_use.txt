📖 Augment New 客户端 - 第一次使用必看指南

⚠️ 重要提醒：缓存清理后的必要操作
=====================================

🔴 关键注意事项：
在使用本客户端清理缓存时，程序会自动关闭您的 Cursor、VSCode 等 IDE 客户端。
清理完成后，您必须按照以下步骤操作，否则可能浪费验证码次数：

1. 重新启动您的 IDE 客户端（Cursor/VSCode 等）
2. 在 IDE 中点击 Augment Code 插件的 "Sign in" 按钮
3. 使用获取到的验证码完成登录

❌ 如果不按照上述步骤操作：
- IDE 可能要求您重新登录 Augment Code
- 这会导致再次消耗验证码次数
- 造成不必要的访问代码使用量浪费

🚀 快速开始流程
===============

1. 获取访问代码
   - 访问 https://afdian.com/a/suaolin 购买访问代码
   - 访问代码格式：XXXX-XXXX-XXXX-XXXX

2. 首次启动程序
   - 同意服务条款
   - 输入访问代码进行验证
   - 验证成功后进入主界面

3. 配置缓存清理（可选）
   - 点击"点击选择同时清理其他应用缓存"按钮
   - 选择要清理的 IDE（VSCode、Cursor、IntelliJ IDEA、PyCharm）
   - 默认清理 VSCode 缓存

4. 执行一键式工作流
   - 点击"一键式工作流"按钮
   - 程序自动执行：
     * 清理选定 IDE 的缓存
     * 生成临时邮箱地址
     * 打开 Augment Code 注册页面
     * 自动监听验证码（最长5分钟）

5. 完成注册和登录
   - 在注册页面使用生成的临时邮箱注册
   - 等待程序自动获取验证码
   - 重新启动您的 IDE 客户端
   - 在 IDE 中使用验证码登录 Augment Code

🔧 界面功能说明
===============

左侧控制面板：
- 访问代码状态和账号余量显示
- 缓存清理配置选项
- 工作流进度指示器
- 一键式工作流按钮

中间日志面板：
- 实时显示操作日志和状态信息
- 可清空日志记录

右侧账号管理：
- 显示已成功获取验证码的邮箱账号
- 可为已注册账号重新获取验证码
- 可删除不需要的账号记录

💡 重要使用提醒
===============

1. 缓存清理后的操作流程
   ⚠️ 这是最重要的部分！
   - 缓存清理会关闭 IDE 进程
   - 必须重新启动 IDE
   - 必须重新点击 Augment Code 的 Sign in
   - 否则会浪费验证码次数

2. 验证码监听
   - 程序自动监听验证码最长 5 分钟
   - 获取成功后会弹窗显示并自动复制到剪贴板
   - 验证码有时效性，请及时使用

3. 邮箱管理
   - 只有成功获取验证码的邮箱才会添加到管理列表
   - 可为同一邮箱多次获取验证码
   - 删除账号不影响实际的 Augment Code 注册状态

⚠️ 常见问题及解决方案
=====================

Q: 为什么需要重新启动 IDE？
A: 缓存清理会关闭 IDE 进程，重启后才能正常使用 Augment Code 插件

Q: 忘记重新启动 IDE 会怎样？
A: IDE 可能要求重新登录，导致额外消耗验证码次数

Q: 验证码获取超时怎么办？
A: 检查网络连接，确认邮箱注册成功，可稍后重试

Q: 访问代码验证失败？
A: 检查代码输入是否正确，确认是否已过期或用完次数

Q: 缓存清理失败？
A: 手动关闭相关 IDE 后重试，或检查是否有权限问题

📞 获取帮助
===========

如遇问题请：
1. 查看程序日志面板的详细信息
2. 确认网络连接正常
3. 验证访问代码有效性
4. 参考"账户被封禁？查看解决方案"

购买访问代码：https://afdian.com/a/suaolin
代售返利计划：https://aug.cursorx.pw/rebate.html

🎯 记住关键步骤：清理缓存 → 重启 IDE → 重新登录 Augment Code
