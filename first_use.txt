📖 Augment New 客户端 - 第一次使用指南

欢迎使用 Augment New 客户端！本指南将帮助您快速上手。

🚀 快速开始
===========

1. 获取访问代码
   - 访问 https://afdian.com/a/suaolin 购买访问代码
   - 访问代码格式类似：XXXX-XXXX-XXXX-XXXX

2. 首次启动
   - 同意服务条款
   - 输入访问代码进行验证
   - 验证成功后进入主界面

3. 配置缓存清理
   - 点击"点击选择同时清理其他应用缓存"按钮
   - 选择要清理的 IDE（VSCode、Cursor、IntelliJ IDEA、PyCharm）
   - 默认会清理 VSCode 缓存

4. 开始使用
   - 点击"一键式工作流"按钮
   - 程序会自动：
     * 清理选定 IDE 的缓存
     * 生成临时邮箱
     * 打开 Augment Code 注册页面
     * 自动监听验证码

🔧 功能说明
===========

左侧控制面板：
- 访问代码状态显示
- 账号余量显示
- 缓存清理配置
- 工作流进度指示器
- 一键式工作流按钮

中间日志面板：
- 实时显示操作日志
- 可以清空日志记录

右侧账号管理：
- 显示已成功获取验证码的账号
- 可以为已注册账号重新获取验证码
- 可以删除不需要的账号记录

💡 使用技巧
===========

1. 邮箱管理
   - 只有成功获取验证码的邮箱才会被添加到管理列表
   - 可以为同一个邮箱多次获取验证码
   - 删除账号只是从列表中移除，不影响实际注册

2. 验证码监听
   - 程序会自动监听验证码最长 5 分钟
   - 获取到验证码后会自动弹出显示
   - 验证码会自动复制到剪贴板

3. 缓存清理
   - 程序会自动关闭相关 IDE 进程
   - 清理用户可访问的缓存文件
   - 以普通用户权限运行，安全可靠

⚠️ 注意事项
============

1. 网络要求
   - 需要稳定的网络连接
   - 确保能访问 Augment Code 服务器

2. 访问代码
   - 访问代码有使用次数限制
   - 请合理使用，避免浪费

3. 临时邮箱
   - 临时邮箱有时效性
   - 建议及时完成注册流程

4. 系统兼容性
   - 支持 Windows、macOS、Linux
   - 需要 Python 3.6+ 环境

🆘 常见问题
===========

Q: 访问代码验证失败？
A: 检查代码是否正确，是否已过期或用完次数

Q: 验证码获取超时？
A: 检查网络连接，或稍后重试

Q: 缓存清理失败？
A: 确保相关 IDE 已关闭，或手动关闭后重试

Q: 程序无法启动？
A: 检查 Python 环境和依赖包是否正确安装

📞 技术支持
===========

如果遇到问题，请：
1. 查看日志面板的错误信息
2. 检查网络连接状态
3. 确认访问代码有效性
4. 联系技术支持

代售返利计划：https://aug.cursorx.pw/rebate.html

感谢使用 Augment New 客户端！
